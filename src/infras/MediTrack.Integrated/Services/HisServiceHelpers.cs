using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Integrated.Services
{
    public class HisServiceHelpers : IHisServiceHelper
    {
        public IHisService CreateHisService(CurrentHospitalDto currentHospital,
            IHttpClientFactory httpClientFactory,
            IDatabaseService databaseService,
            ICachedService cachedService,
            string logPrefix,
            IHospitalMetadataRepository? hospitalMetadataRepository = null,
            IProvinceRepository? provinceRepository = null,
            IDistrictRepository? districtRepository = null,
            IWardRepository? wardRepository = null)
        {
            var config = new HisServiceConfiguration
            {
                CurrentHospital = currentHospital,
                DatabaseService = databaseService,
                HttpClientFactory = httpClientFactory,
                LogPrefix = logPrefix,
                CachedService = cachedService,
                HospitalMetadataRepository = hospitalMetadataRepository,
                ProvinceRepository = provinceRepository,
                DistrictRepository = districtRepository,
                WardRepository = wardRepository
            };

            return currentHospital.HisVersion switch
            {
                "v1" => new HisService(config),
                "v2" => new HisV2Service(config),
                "v3" => new HisV3Service(config),
                "v4" => new HisV4Service(config),
                "v5" => new HisV5Service(config),
                "v6" => new HisV6Service(config),
                "v7" => new HisV7Service(config),
                "v8" => new HisV8Service(config),
                "v9" => new HisV9Service(config),
                "v10" => new HisV10Service(config),
                "v11" => new HisV11Service(config),
                "v12" => new HisV12Service(config),
                "v13" => new HisV13Service(config),
                "v14" => new HisV14Service(config),
                "v15" => new HisV15Service(config),
                "v16" => new HisV16Service(config),
                "v17" => new HisV17Service(config),
                "v18" => new HisV18Service(config),
                "v19" => new HisV19Service(config),
                "v21" => new HisV21Service(config),
                "v22" => new HisV22Service(config),
                "v23" => new HisV23Service(config),
                "v24" => new HisV24Service(config),
                "v27" => new HisV27Service(config),
                _ => new HisV7Service(config)
            };
        }


        public CurrentHospitalDto GetHospitalServiceById(string hospitalId, IDatabaseService databaseService)
        {
            var current = DataStorageObject.GetCurrentHospital(hospitalId);
            if (current is not null)
            {
                return current;
            }

            var hospital = GetHospital(hospitalId, databaseService).Result;

            current = new CurrentHospitalDto
            {
                HospitalId = hospitalId,
                IsGenQueueNumber = hospital?.IsGenQueueNumberDefault ?? false,
                IsGenQR = hospital?.IsGenQR ?? false,
                IsSkipGetServices = hospital?.IsSkipGetServices ?? false,
                IsSkipGetInsuranceServices = hospital?.IsSkipGetInsuranceServices ?? false,
                IsIgnoreInsurancePayment = hospital?.IsIgnoreInsurancePayment ?? false,
                IsShowCareer = hospital?.IsShowCareer ?? false,
                IsShowSocialCareer = hospital?.IsShowSocialCareer ?? false,
                HisVersion = hospital?.HisVersion ?? string.Empty,
                HisConfig = hospital?.HisConfig ?? [],
                IsMaintenanceMode = hospital?.IsMaintenanceMode ?? false,
                IsSupportInsurance = hospital?.IsSupportInsuranceDefault ?? false,
                IsAdvancePayment = hospital?.IsAdvancePayment ?? false,
                IsInsuranceAdvancePayment = hospital?.IsInsuranceAdvancePayment ?? false,
                IsGenQueueNumberByHis = hospital?.IsGenQueueNumberByHis ?? false,
                IsGenQRWhenCreateRegister = hospital?.IsGenQRWhenCreateRegister ?? false,
                IsUseExtraFeeAsInsurancePrice = hospital?.IsUseExtraFeeAsInsurancePrice ?? false,
                IsBlockForDuplicatedVisitInsurance = hospital?.IsBlockForDuplicatedVisitInsurance ?? false,
                IsAllowCustomerRetryByPatientCode = hospital?.IsAllowCustomerRetryByPatientCode ?? false,
                IsAllowCustomerRetryByInsuranceCode = hospital?.IsAllowCustomerRetryByInsuranceCode ?? false,
                FaceMatchingRateAccepted = hospital?.FaceMatchingRateAccepted,
                IsBlockInsuranceOnWeekend = hospital?.IsBlockInsuranceOnWeekend ?? false,
                IsAllowPushQueueInfoToHis = hospital?.IsAllowPushQueueInfoToHis ?? false,
                IsHideQrCodePaymentDefault = hospital?.IsHideQrCodePaymentDefault ?? false,
                IsRegisterRelativeDefault = hospital?.IsRegisterRelativeDefault ?? false,
                IsNeedAuthenticateBeforeGetQueueNumber = hospital?.IsNeedAuthenticateBeforeGetQueueNumber ?? false,
                IsActiveConfigExaminationTime = hospital?.IsActiveConfigExaminationTime ?? false,
                IsAllowBypassAdvancePayment = hospital?.IsAllowBypassAdvancePayment ?? false,
                IsTwoLevelAddress = hospital?.IsTwoLevelAddress ?? false,
                IsShowAccidentCode = hospital?.IsShowAccidentCode ?? false,
                IsShowBloodType = hospital?.IsShowBloodType ?? false,
                IsInsurancePopupHiddenOnCorrectReferral = hospital?.IsInsurancePopupHiddenOnCorrectReferral ?? false,
                IsInsurancePaymentInAppAllowed = hospital?.IsInsurancePaymentInAppAllowed ?? false,
                IsServicePaymentInAppAllowed = hospital?.IsServicePaymentInAppAllowed ?? false,
                IsInputDiagnosisVisible = hospital?.IsInputDiagnosisVisible ?? false,
                IsAllowOnlyOneQueuePerDay = hospital?.IsAllowOnlyOneQueuePerDay ?? false,
                IsShowChatBot = hospital?.IsShowChatBot ?? false,

            };

            DataStorageObject.AddOrUpdateCurrentHospital(current);

            return current;
        }

        public async Task<Hospital?> GetHospital(string hospitalId, IDatabaseService databaseService, CancellationToken cancellationToken = default)
        {
            var hospital = DataStorageObject.GetHospital(hospitalId);
            if (hospital is not null)
            {
                return hospital;
            }

            hospital = await databaseService.Hospitals
                .AsNoTracking()
                .FirstOrDefaultAsync(h => h.Id == hospitalId, cancellationToken);

            if (hospital is null)
            {
                return null;
            }

            DataStorageObject.AddOrUpdateHospital(hospital);

            return hospital;
        }

        public async Task<Kiosk?> GetKiosk(string kioskId, IDatabaseService databaseService, CancellationToken cancellationToken = default)
        {
            var kiosk = DataStorageObject.GetKiosk(kioskId);
            if (kiosk is not null)
            {
                return kiosk;
            }

            kiosk = await databaseService.Kiosks
                .AsNoTracking()
                .FirstOrDefaultAsync(k => k.Id == kioskId, cancellationToken);

            if (kiosk is null)
            {
                return null;
            }

            DataStorageObject.AddOrUpdateKiosk(kiosk);

            return kiosk;
        }

        public async Task<List<Kiosk>?> GetKiosks(string hospitalId, IDatabaseService databaseService)
        {
            var kiosks = DataStorageObject.GetKiosks(hospitalId);
            if (kiosks is not null)
            {
                return kiosks;
            }

            kiosks = await databaseService.Kiosks
                .AsNoTracking()
                .Where(k => k.HospitalId == hospitalId)
                .ToListAsync();

            if (kiosks is null)
            {
                return null;
            }

            foreach (var kiosk in kiosks)
            {
                DataStorageObject.AddOrUpdateKiosk(kiosk);
            }

            return kiosks;
        }

        public Task ResetHospitalCache(string hospitalId)
        {
            DataStorageObject.RemoveHospital(hospitalId);
            return Task.CompletedTask;
        }

        public Task ResetKioskCache(string kioskId)
        {
            DataStorageObject.RemoveKiosk(kioskId);
            return Task.CompletedTask;
        }

        public Task ResetKiosksCache(string hospitalId)
        {
            var kiosks = DataStorageObject.GetKiosks(hospitalId);
            if (kiosks is not null)
            {
                foreach (var kiosk in kiosks)
                {
                    DataStorageObject.RemoveKiosk(kiosk.Id);
                }
            }

            return Task.CompletedTask;
        }
    }
}