﻿using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;
using MediTrack.Domain.Helpers;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Enums;
using System.Text.RegularExpressions;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Tương tự v7, <PERSON>ịch vụ thường hiển thị khi khám BHYT (Khác nhau về exameTypeId)
    /// BV này có response các id khác với request
    /// ExameTypeId: id_loai_kham (API HealthService, GetHealthServices)
    /// ExameTypeIdRes: id_loai_kham (API RegisterForm, CreateRegisterForm)
    /// </summary>
    public class HisV27Service : HisV7Service
    {
        private readonly HisV7Config hisConfig;

        public HisV27Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV27Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];
            string url = $"{hisConfig.Host}/phong-kham";
            var req = new GetHealthServiceRequest()
            {
                id_khoa = request.ClinicId ?? string.Empty,
                ma_khoa = request.ClinicCode ?? string.Empty,
                id_loai_kham = request.ExameTypeId ?? string.Empty,
                ma_the_bhyt = request.HealthInsuranceNo ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthServices Req --- {@Request}", logPrefix, req);

            (result, message, List<HealthServiceModel> resData)
                = await httpClientFactory.CreateClient().GetHealthService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);
            if (result)
            {
                var hospitalMetaDatas = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(current.HospitalId)
                : null;

                var servicePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "SERVICE");
                var insurancePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "INSURANCE");

                list = [.. resData!.OrderBy(x => x.thu_tu_sap_xep ?? 0)
                .Select(x => new HealthService()
                    {
                        Id = x.id_phong_kham ?? string.Empty,
                        Code = x.ma_phong_kham ?? string.Empty,
                        Name = x.ten_phong_kham ?? string.Empty,
                        UnitPrice = current.IsAdvancePayment ? null : x.don_gia_phong_kham,
                        UnitPriceDisplay = servicePriceDisplay?.Value
                            ?? (current.IsAdvancePayment ? "Tạm ứng" : (x.don_gia_phong_kham.ToString("N0") + " đ")),
                        InsurancePrice = current.IsInsuranceAdvancePayment ? null : (current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt),
                        InsurancePriceDisplay = insurancePriceDisplay?.Value
                            ?? (current.IsInsuranceAdvancePayment
                                ? "Tạm ứng" : ((current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt).ToString("N0") + " đ")),
                        ExtraPrice = x.don_gia_thu_them,
                        IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                        ExameTypeId = request.ExameTypeId ?? string.Empty, // giữ lại đối tượng khám để dùng get lại khi đăng kí 354
                        ExameTypeIdRes = x.id_loai_kham ?? string.Empty,
                        ClinicGroupId = x.id_nhom_phong_kham ?? string.Empty,
                        ClinicGroupCode = x.ma_nhom_phong_kham ?? string.Empty,
                        ClinicGroup = x.ten_nhom_phong_kham ?? string.Empty,
                        ClinicId = request.ClinicId ?? string.Empty,
                        ClinicCode = request.ClinicCode ?? string.Empty,
                        SubClinicId = request.SubClinicId ?? string.Empty,
                        ExaminationHour = string.Empty,
                        WaitingPatientCount = (x.sl_hien_tai.HasValue && x.sl_da_kham.HasValue)
                                                ? Math.Max(x.sl_hien_tai.Value - x.sl_da_kham.Value, 0)
                                                : null,
                        RemainingPatientCount = (x.sl_toi_da.HasValue && x.sl_hien_tai.HasValue)
                                                ? Math.Max(x.sl_toi_da.Value - x.sl_hien_tai.Value, 0)
                                                : null,
                        TotalPatientCount = x.sl_toi_da,
                        ProcessingNumber = x.sl_hien_tai
                })];
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;
            var doiTuongKcb = request.MedicalTreatmentCategoryId;

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcb = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                : null;

                doiTuongKcb = defaultDoiTuongKcb?.Value;
            }

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                    : null;

                if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                {
                    var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                    if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                    {
                        doiTuongKcb = doiTuongKCBMap.ContainsKey(request.Service.ExameTypeId ?? string.Empty)
                            ? doiTuongKCBMap[request.Service.ExameTypeId ?? string.Empty] : "1";
                    }
                }
            }

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName().ToUpper(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = careerId,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = request.BloodType ?? string.Empty,
                    so_gttt = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    ly_do_vv = request.ReasonForVisit ?? string.Empty,
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = doiTuongKcb ?? "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                    phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                    cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                    cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                    cs_nhiet_do = request.Temperature.ToString() ?? string.Empty,
                    cs_mach = request.PulseRate.ToString() ?? string.Empty,
                    uu_tien = request.Priority,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    ma_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_nhom_phong_kham = request.Service.ClinicGroupId ?? string.Empty,
                    ma_nhom_phong_kham = request.Service.ClinicGroupCode ?? string.Empty,
                    ten_nhom_phong_kham = request.Service.ClinicGroup ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeIdRes ?? string.Empty, //lấy id_loai_kham từ response của GetHealthServices
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                bn_dichvu = !request.IsInsurance,
                ma_tai_nan = request.AccidentCode ?? string.Empty,
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = registerDto.thong_tin_benh_nhan.uu_tien?.ToString() ?? request.Priority.ToString(),
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = registerDto.thong_tin_dang_ky.ten_khu_vuc ?? string.Empty,
                    MedicalTreatmentCategoryName = registerDto.thong_tin_dang_ky.ten_doi_tuong ?? string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }
    }
}