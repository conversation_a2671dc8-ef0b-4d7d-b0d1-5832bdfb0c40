﻿using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations;

public class DonationCampaignConfiguration : IEntityTypeConfiguration<DonationCampaign>
{
    public void Configure(EntityTypeBuilder<DonationCampaign> builder)
    {
        builder.<PERSON><PERSON>ey(x => x.Id);
        builder.HasIndex(x => x.Id)
            .HasDatabaseName("PK_DonationCampaign");

        builder.Property(n => n.Title)
            .IsRequired()
            .HasMaxLength(256);

        builder.HasIndex(x => x.HospitalId)
            .HasDatabaseName("IX_DonationCampaign_HospitalId");
        builder.HasIndex(x => x.DonationCompaignTypeId)
            .HasDatabaseName("IX_DonationCampaign_DonationCompaignTypeId");

        builder.HasQueryFilter(x => x.IsDeleted != true);

        builder.Property(n => n.ImageUrls)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

        builder.Property(n => n.FileUrls)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

        builder.HasMany(n => n.DonationHistories)
            .WithOne(r => r.DonationCampaign)
            .HasForeignKey(r => r.CampaignId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(n => n.DonationFollowers)
            .WithOne(r => r.DonationCampaign)
            .HasForeignKey(r => r.DonationCampaignId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(x => x.CreatedBy)
            .HasMaxLength(50);
        builder.Property(x => x.UpdatedBy)
            .HasMaxLength(50);

        builder.Property(x => x.CreatedAt)
           .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        builder.Property(x => x.CreatedBy)
            .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
    }
}
