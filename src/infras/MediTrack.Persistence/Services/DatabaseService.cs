﻿using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Reflection;

namespace MediTrack.Persistence.Services
{
    public class DatabaseService : IdentityDbContext<User, Role, string>, IDatabaseService
    {
        public DatabaseService() { }

        public DatabaseService(DbContextOptions<DatabaseService> options)
            : base(options) { }

        public DbSet<Hospital> Hospitals { get; set; } = default!;
        public DbSet<Clinic> Clinics { get; set; } = default!;
        public DbSet<Register> Registers { get; set; } = default!;
        public DbSet<Customer> Customers { get; set; } = default!;
        public DbSet<Payment> Payments { get; set; } = default!;
        public DbSet<Receipt> Receipts { get; set; } = default!;
        public DbSet<HealthService> HealthServices { get; set; } = default!;
        public DbSet<ServiceClient> ServiceClients { get; set; } = default!;
        public DbSet<RefreshToken> RefreshTokens { get; set; } = default!;
        public DbSet<Province> Provinces { get; set; } = default!;
        public DbSet<District> Districts { get; set; } = default!;
        public DbSet<Ward> Wards { get; set; } = default!;
        public DbSet<ObjectPermission> ObjectPermissions { get; set; } = default!;
        public DbSet<Nationality> Nationalities { get; set; } = default!;
        public DbSet<Nation> Nations { get; set; } = default!;
        public DbSet<Kiosk> Kiosks { get; set; } = default!;
        public DbSet<AppHome> AppHomes { get; set; } = default!;
        public DbSet<AppBanner> AppBanners { get; set; } = default!;
        public DbSet<HospitalMetaData> HospitalMetaDatas { get; set; } = default!;
        public DbSet<Career> Careers { get; set; } = default!;
        public DbSet<Otp> Otps { get; set; } = default!;
        public DbSet<Relationship> Relationships { get; set; } = default!;
        public DbSet<CustomerRelationShip> CustomerRelationShips { get; set; } = default!;
        public DbSet<DialerQueue> DialerQueues { get; set; } = default!;
        public DbSet<CustomerHospital> CustomerHospitals { get; set; } = default!;
        public DbSet<His> His { get; set; } = default!;
        public DbSet<HisMetaData> HisMetaDatas { get; set; } = default!;
        public DbSet<SystemMetaData> SystemMetaDatas { get; set; } = default!;
        public DbSet<BankBranch> BankBranches { get; set; } = default!;
        public DbSet<ForgotPasswordSession> ForgotPasswordSessions { get; set; } = default!;
        public DbSet<HospitalUser> HospitalUsers { get; set; } = default!;
        public DbSet<KioskMetaData> KioskMetaDatas { get; set; } = default!;
        public DbSet<HealthServiceMetaData> HealthServiceMetaDatas { get; set; } = default!;
        public DbSet<RegisterDetail> RegisterDetails { get; set; } = default!;
        public DbSet<EquipmentCatalog> EquipmentCatalogs { get; set; } = default!;
        public DbSet<ExportedEquipment> ExportedEquipments { get; set; } = default!;
        public DbSet<ExportedEquipmentDetail> ExportedEquipmentDetails { get; set; } = default!;
        public DbSet<ImportedEquipment> ImportedEquipments { get; set; } = default!;
        public DbSet<ImportedEquipmentDetail> ImportedEquipmentDetails { get; set; } = default!;
        public DbSet<WarehousePartner> WarehousePartners { get; set; } = default!;
        public DbSet<EquipmentVendor> EquipmentVendors { get; set; } = default!;
        public DbSet<KioskReleaseHistory> KioskReleaseHistories { get; set; } = default!;
        public DbSet<WarehouseEquipment> WarehouseEquipments { get; set; } = default!;
        public DbSet<ImportedEquipmentHistory> ImportedEquipmentHistories { get; set; } = default!;
        public DbSet<ExportedEquipmentHistory> ExportedEquipmentHistories { get; set; } = default!;
        public DbSet<EquipmentModel> EquipmentModels { get; set; } = default!;
        public DbSet<EquipmentWarranty> EquipmentWarranties { get; set; } = default!;
        public DbSet<EquipmentRevoke> EquipmentRevokes { get; set; } = default!;
        public DbSet<EquipmentRevokeDetail> EquipmentRevokeDetails { get; set; } = default!;
        public DbSet<EquipmentRevokeHistory> EquipmentRevokeHistories { get; set; } = default!;
        public DbSet<KioskStatusLog> KioskStatusLogs { get; set; } = default!;
        public DbSet<CustomerReview> CustomerReviews { get; set; } = default!;
        public DbSet<PatientBooking> PatientBookings { get; set; } = default!;
        public DbSet<AdvertisingPartner> AdvertisingPartners { get; set; } = default!;
        public DbSet<AdvertisingPartnerMetaData> AdvertisingPartnerMetaDatas { get; set; } = default!;
        public DbSet<AdvertisingCampaign> AdvertisingCampaigns { get; set; } = default!;
        public DbSet<AdvertisingPartnerCampaign> AdvertisingPartnerCampaigns { get; set; } = default!;
        public DbSet<Membership> Memberships { get; set; } = default!;
        public DbSet<PackageService> PackageServices { get; set; } = default!;
        public DbSet<HealthPackageDetail> HealthPackageDetails { get; set; } = default!;
        public DbSet<ExameType> ExameTypes { get; set; } = default!;
        public DbSet<HospitalPatientBooking> HospitalPatientBookings { get; set; } = default!;
        public DbSet<HospitalPatientBookingConfig> HospitalPatientBookingConfigs { get; set; } = default!;
        public DbSet<HospitalNotification> HospitalNotifications { get; set; } = default!;
        public DbSet<AdvertisingKioskCampaign> CampaignParticipations { get; set; } = default!;
        public DbSet<UserShareInfo> UserShareInfors { get; set; } = default!;
        public DbSet<SessionMembershipConfirm> SessionMembershipConfirms { get; set; } = default!;
        public DbSet<CustomerFlow> CustomerFlows { get; set; } = default!;
        public DbSet<AppNotificationHistory> AppNotificationHistories { get; set; } = default!;
        public DbSet<DonationCampaign> DonationCampaigns { get; set; } = default!;
        public DbSet<DonationFollower> DonationFollowers { get; set; } = default!;
        public DbSet<DonationHistory> DonationHistories { get; set; } = default!;
        public DbSet<DonationCampaignType> DonationCampaignTypes { get; set; } = default!;
        public DbSet<DialerQueueHistory> DialerQueueHistories { get; set; } = default!;
        public DbSet<MembershipHistory> MembershipHistories { get; set; } = default!;

        // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        // {
        //     optionsBuilder.UseNpgsql();
        // }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                    {
                        property.SetValueConverter(new DateTimeToUtcConverter());
                    }
                }
            }
        }

    }

    public class DateTimeToUtcConverter : ValueConverter<DateTime, DateTime>
    {
        public DateTimeToUtcConverter() : base(
            v => v.ToUniversalTime(),
            v => DateTime.SpecifyKind(v, DateTimeKind.Utc))
        {
        }
    }
}
