﻿using System.Security.Cryptography;
using System.Text;

namespace MediTrack.Ultils.Helpers
{
    public class SignatureHelper
    {
        public static string BcryptHash(string input) =>
            BCrypt.Net.BCrypt.HashPassword(input);

        public static bool IsValidBcrypt(string input, string hashInput) =>
            BCrypt.Net.BCrypt.Verify(input, hashInput);

        /// <summary>
        /// Hash text using SHA256
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Sha256Hash(string input)
        {
            byte[] bytes = SHA256.HashData(Encoding.UTF8.GetBytes(input));
            StringBuilder builder = new();
            for (int i = 0; i < bytes.Length; i++)
            {
                builder.Append(bytes[i].ToString("x2"));
            }
            return builder.ToString();
        }

        public static string HmacSha256Hash(string input, string key)
        {
            using HMACSHA256 hmac = new(Encoding.UTF8.GetBytes(key));
            byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(input));
            return BitConverter.ToString(hashValue).Replace("-", "").ToLower();
        }

        public static string HmacSha256HashV2(string input, string key)
        {
            key ??= "";
            var encoding = new ASCIIEncoding();
            byte[] keyByte = encoding.GetBytes(key);
            byte[] messageBytes = encoding.GetBytes(input);
            using var hmacsha256 = new HMACSHA256(keyByte);
            byte[] hashmessage = hmacsha256.ComputeHash(messageBytes);
            string sbinary = "";
            for (int i = 0; i < hashmessage.Length; i++)
            {
                sbinary += hashmessage[i].ToString("x2");
            }
            return sbinary;
        }

        public static string HmacSha512Hash(string input, string key)
        {
            var hash = new StringBuilder();
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            using (var hmac = new HMACSHA512(keyBytes))
            {
                byte[] hashValue = hmac.ComputeHash(inputBytes);
                foreach (var theByte in hashValue)
                {
                    hash.Append(theByte.ToString("x2"));
                }
            }

            return hash.ToString();
        }

        public static bool IsValidHamcSha256Hash(string input, string hash, string key)
        {
            using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key)))
            {
                byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(input));
                return BitConverter.ToString(hashValue).Replace("-", "").Equals(hash, StringComparison.CurrentCultureIgnoreCase);
            }
        }

        public static string ComputeMD5Hash(string input)
        {
            // Sử dụng MD5 để tạo đối tượng MD5
            // Chuyển đổi chuỗi đầu vào thành mảng byte
            byte[] inputBytes = Encoding.ASCII.GetBytes(input);
            // Tính toán hash từ mảng byte
            byte[] hashBytes = MD5.HashData(inputBytes);

            // Chuyển đổi mảng byte thành chuỗi hex
            StringBuilder sb = new();
            for (int i = 0; i < hashBytes.Length; i++)
            {
                sb.Append(hashBytes[i].ToString("x2"));
            }
            return sb.ToString();
        }
    }
}
