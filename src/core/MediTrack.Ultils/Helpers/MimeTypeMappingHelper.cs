﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Helpers
{
    public static class MimeTypeMappingHelper
    {
        private static readonly Dictionary<string, string> Mappings = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase)
        {
            {".jpg", "image/jpeg"},
            {".jpeg", "image/jpeg"},
            {".png", "image/png"},
            {".gif", "image/gif"},
            {".bmp", "image/bmp"},
            {".txt", "text/plain"},
            {".pdf", "application/pdf"},
            {".doc", "application/msword"},
            {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
            {".xls", "application/vnd.ms-excel"},
            {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
            {".ppt", "application/vnd.ms-powerpoint"},
            {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
            {".csv", "text/csv"},
            {".zip", "application/zip"},
            {".mp3", "audio/mpeg"},
            {".mp4", "video/mp4"},
        };

        public static string GetMimeType(string fileExtension)
        {
            if (Mappings.TryGetValue(fileExtension, out string? mimeType))
            {
                return mimeType ?? string.Empty;
            }
            return "application/octet-stream"; // Default value
        }
    }
}
