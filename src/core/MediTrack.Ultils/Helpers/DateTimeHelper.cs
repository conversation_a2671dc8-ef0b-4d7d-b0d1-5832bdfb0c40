﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Helpers
{
    public static class DateTimeHelper
    {
        public static string FormatDate(string input, string inputFormat, string outputFormat)
        {
            // Try to parse the input string with the expected format
            bool success = DateTime.TryParseExact(input, inputFormat, null, DateTimeStyles.None, out DateTime dateTime);

            if (success)
            {
                // Format the parsed DateTime to "dd/MM/yyyy"
                return dateTime.ToString(outputFormat);
            }
            else
            {
                // Return "" if parsing fails
                return string.Empty;
            }
        }

        //Get current date time in Vietnam
        public static DateTime GetCurrentLocalDateTime()
        {
            return DateTime.UtcNow.AddHours(7);
        }
        
        //Convert string to DateTime
        public static DateTime? ConvertStringToDateTime(this string input, string format)
        {
            if (DateTime.TryParseExact(input, format, null, DateTimeStyles.None, out DateTime dateTime))
            {
                return dateTime;
            }
            
            return null;
        }
        public static int GetTimeByString(this string time)
        {
            //convert hh:MM to int
            var timeArr = time.Split(":");
            return int.Parse(timeArr[0]) * 60 + int.Parse(timeArr[1]);
        }

        public static bool IsWeekend(this DateTime date)
        {
            return date.DayOfWeek == DayOfWeek.Saturday 
                || date.DayOfWeek == DayOfWeek.Sunday;
        }

        public static bool IsAgeInRange(this DateTime dateOfBirth, int? minAge = null, int? maxAge = null)
        {
            DateTime today = GetCurrentLocalDateTime();
            int age = today.Year - dateOfBirth.Year; 
            if (today < dateOfBirth.AddYears(age))
            {
                age--;
            }

            if ((minAge.HasValue && age < minAge.Value) || (maxAge.HasValue && age > maxAge.Value))
            {
                return false;
            }
            
            return true;
        }
    }
}
