namespace MediTrack.Ultils.Helpers
{
    public static class TelegramHelper
    {
        public static async Task<string> SendAsync(
            this HttpClient client,
            string apiKey,
            string chatId,
            string message)
        {
            string messageError = string.Empty;
            try
            {
                var url = $"https://api.telegram.org/bot{apiKey}/sendMessage?chat_id={chatId}&text={message}";
                var response = await client.GetAsync(url);

                if(!response.IsSuccessStatusCode)
                {
                    messageError = response.ReasonPhrase ?? "Error";
                }

                return messageError;
            }
            catch (Exception ex)
            {
                messageError = ex.Message;
                return messageError;
            }
        }
    }
}