using System.Net.Http.Headers;
using System.Text;
using System.Web;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace MediTrack.Ultils.Helpers
{
    /// <summary>
    /// HttpClientHelper for sending http request POST, GET
    /// </summary>
    public class HttpClientHelper
    {
        /// <summary>
        /// Send http request POST
        /// </summary>
        /// <param name="httpClient">HttpClient</param>
        /// <param name="url">Url</param>
        /// <param name="headers">Headers</param>
        /// <param name="data">Body data type K</param>
        /// <param name="jsonSerializerSettings">JsonSerializerSettings</param>
        /// <returns>Response type T</returns>
        public static async Task<(bool, string, T)> PostAsync<T, K>(HttpClient httpClient, 
            string url, 
            Dictionary<string, string> headers, 
            K data,
            JsonSerializerSettings? jsonSerializerSettings = null)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(
                    JsonConvert.SerializeObject(data, jsonSerializerSettings), 
                    Encoding.UTF8, 
                    "application/json")
            };

            foreach (var header in headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }

            var response = await httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync() ?? string.Empty;
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<T>(responseContent, jsonSerializerSettings);
                return (true, responseContent, result!);
            }

            return (false, responseContent, default!);
        }

        /// <summary>
        /// Send http file request POST
        /// </summary>
        /// <param name="httpClient">HttpClient</param>
        /// <param name="url">Url</param>
        /// <param name="headers">Headers</param>
        /// <param name="IFormFile">file</param>
        /// <param name="jsonSerializerSettings">JsonSerializerSettings</param>
        /// <returns>Response type T</returns>
        public static async Task<(bool, string, T)> PostFileAsync<T>(HttpClient httpClient, 
            string url, 
            Dictionary<string, string> headers, 
            IFormFile file,
            JsonSerializerSettings? jsonSerializerSettings = null)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            var form = new MultipartFormDataContent
            {
                {
                    new StreamContent(file.OpenReadStream())
                    {
                        Headers =
                        {
                            ContentLength = file.Length,
                            ContentType = new MediaTypeHeaderValue(file.ContentType)
                        }
                    },
                    "file",
                    file.FileName
                }
            };
            request.Content = form;
            foreach (var header in headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }

            var response = await httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<T>(responseContent, jsonSerializerSettings);
                return (true, responseContent, result!);
            }
            return (false, responseContent, default!);
        }

        /// <summary>
        /// Send http files request POST
        /// </summary>
        /// <param name="httpClient">HttpClient</param>
        /// <param name="url">Url</param>
        /// <param name="headers">Headers</param>
        /// <param name="files">Dictionary of files</param>
        /// <param name="jsonSerializerSettings">JsonSerializerSettings</param>
        /// <returns>Response type T</returns>
        public static async Task<(bool, string, T)> PostFilesAsync<T>(HttpClient httpClient, 
            string url, 
            Dictionary<string, string> headers, 
            Dictionary<string, IFormFile> files,
            JsonSerializerSettings? jsonSerializerSettings = null)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            var form = new MultipartFormDataContent();
            foreach (var file in files)
            {
                form.Add(new StreamContent(file.Value.OpenReadStream())
                {
                    Headers =
                    {
                        ContentLength = file.Value.Length,
                        ContentType = new MediaTypeHeaderValue(file.Value.ContentType)
                    }
                }, file.Key, file.Value.FileName);
            }
            request.Content = form;
            foreach (var header in headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }

            var response = await httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<T>(responseContent, jsonSerializerSettings);
                return (true, responseContent, result!);
            }

            return (false, responseContent, default!);
        }
        

        /// <summary>
        /// Send http request GET
        /// </summary>
        /// <param name="httpClient">HttpClient</param>
        /// <param name="url">Url</param>
        /// <param name="headers">Headers</param>
        /// <param name="queries">Queries</param>
        /// <param name="jsonSerializerSettings">JsonSerializerSettings</param>
        /// <returns>Response type (bool, string, T)</returns>
        public static async Task<(bool, string, T)> GetAsync<T>(HttpClient httpClient, 
            string url,
            Dictionary<string, string> headers,
            Dictionary<string, string> queries,
            JsonSerializerSettings? jsonSerializerSettings = null)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            foreach (var header in headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }
            var uriBuilder = new UriBuilder(request.RequestUri!);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            foreach (var q in queries)
            {
                query[q.Key] = q.Value;
            }

            uriBuilder.Query = query.ToString();
            request.RequestUri = uriBuilder.Uri;

            var response = await httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<T>(responseContent, jsonSerializerSettings);
                return (true, responseContent, result!);
            }
            return (false, responseContent, default!);
        }
    }
}