using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Helpers
{
    public class PrintHelper
    {
        public static Tuple<string, string> SplitStringByMaxCharacters(string input, int maxCharacters)
        {
            int splitIndex = FindCommaNearLimit(input, maxCharacters);

            if (splitIndex == -1)
            {
                return new Tuple<string, string>(input, string.Empty);
            }

            string firstPart = input.Substring(0, splitIndex + 1).Trim(); // +1 to include the comma
            string remainingPart = input.Substring(splitIndex + 1).Trim();

            return new Tuple<string, string>(firstPart, remainingPart);
        }

        public static string AddressConvert(string input)
        {
            string address = input;// "218A/1C Tổ Dân Phố 18 KP4, An <PERSON>, Quận 12, TP. <PERSON><PERSON>";
            if (input == "-")
            {
                return input;
            }
            var results = "";
            var part = 0;
            for (int i = 0; i < 3; i++)
            {
                part++;
                if (part == 1)
                {
                    if (FindCommaNearLimit(address, 15) != -1)
                    {
                        var splitRessult = SplitStringByMaxCharacters(address, 15);
                        address = splitRessult.Item2;
                        results = splitRessult.Item1;
                    }
                    else
                    {
                        results += address;
                        break;
                    }
                }
                if (part == 2)
                {
                    if (FindCommaNearLimit(address, 15) != -1)
                    {
                        var splitRessult = SplitStringByMaxCharacters(address, 15);
                        address = splitRessult.Item2;
                        results += $"\n{splitRessult.Item1}";
                    }
                    else
                    {
                        results += $"\n{address}";
                        break;
                    }
                }
                if (part == 3)
                {
                    if (FindCommaNearLimit(address, 15) != -1)
                    {
                        var splitRessult = SplitStringByMaxCharacters(address, 40);
                        address = splitRessult.Item2;
                        results += $"\n{splitRessult.Item1}\n{splitRessult.Item2}";
                    }
                    else
                    {
                        results += $"\n{address}";
                        break;
                    }
                }
            }
            return results;
        }

        private static int FindCommaNearLimit(string input, int limit)
        {
            if (input.Length <= limit)
            {
                return -1; // input.LastIndexOf(',') != -1 ? input.LastIndexOf(',') : input.Length - 1;
            }

            int beforeIndex = input.LastIndexOf(',', limit);
            int afterIndex = input.IndexOf(',', limit);

            // Choose the nearest comma if both before and after exist, otherwise choose the one that exists
            if (beforeIndex != -1 && afterIndex != -1)
            {
                return (limit - beforeIndex) <= (afterIndex - limit) ? beforeIndex : afterIndex;
            }
            else if (beforeIndex != -1)
            {
                return beforeIndex;
            }
            else if (afterIndex != -1)
            {
                return afterIndex;
            }
            else
            {
                return limit;
            }
        }

        public static string GetPaymentStatusDesc(string paymentStatus, bool isInsurance = false)
        {
            return paymentStatus switch
            {
                "FAIL" => "Chưa thu",
                "SUCCESS" => isInsurance ? "Thành công" : "Đã thu",
                "PARTIAL" => "Đã thu một phần",
                _ => "Chưa thu"
            };
        }

        public static string GetPaymentStatusJsonMapDesc(string paymentStatus, string value)
        {
            var mappjson = JsonConvert.DeserializeObject(value).ToString();
            var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(mappjson);

            return dict[paymentStatus].ToString();
        }

        public static string ServiceNameConvert(string input, int length = 15)
        {
            string address = input;// "218A/1C Tổ Dân Phố 18 KP4, An Phú Đông, Quận 12, TP. Hồ Chí Minh";
            if (input == "-")
            {
                return input;
            }
            var results = "";
            var part = 0;
            for (int i = 0; i < 3; i++)
            {
                part++;
                if (part == 1)
                {
                    if (FindBackSpaceNearLimit(address, length) != -1)
                    {
                        var splitRessult = SplitStringByBackSpaceMaxCharacters(address, length);
                        address = splitRessult.Item2;
                        results = splitRessult.Item1;
                    }
                    else
                    {
                        results += address;
                        break;
                    }
                }
                if (part == 2)
                {
                    if (FindBackSpaceNearLimit(address, length) != -1)
                    {
                        var splitRessult = SplitStringByBackSpaceMaxCharacters(address, length);
                        address = splitRessult.Item2;
                        results += $"\n{splitRessult.Item1}";
                    }
                    else
                    {
                        results += $"\n{address}";
                        break;
                    }
                }
                if (part == 3)
                {
                    if (FindBackSpaceNearLimit(address, length) != -1)
                    {
                        var splitRessult = SplitStringByBackSpaceMaxCharacters(address, 40);
                        address = splitRessult.Item2;
                        results += $"\n{splitRessult.Item1}\n{splitRessult.Item2}";
                    }
                    else
                    {
                        results += $"\n{address}";
                        break;
                    }
                }
            }
            return results;
        }

        public static int FindBackSpaceNearLimit(string input, int limit)
        {
            // Nếu độ dài của chuỗi nhỏ hơn hoặc bằng giới hạn, không cần tách chuỗi
            if (input.Length <= limit)
            {
                return -1;
            }

            // Tìm khoảng trắng cuối cùng trước giới hạn
            int beforeIndex = input.LastIndexOf(' ', limit);

            // Tìm khoảng trắng đầu tiên sau giới hạn
            int afterIndex = input.IndexOf(' ', limit);

            // Chọn khoảng trắng gần nhất nếu cả hai đều tồn tại
            if (beforeIndex != -1 && afterIndex != -1)
            {
                return (limit - beforeIndex) <= (afterIndex - limit) ? beforeIndex : afterIndex;
            }
            // Nếu chỉ có beforeIndex tồn tại, trả về nó
            else if (beforeIndex != -1)
            {
                return beforeIndex;
            }
            // Nếu chỉ có afterIndex tồn tại, trả về nó
            else if (afterIndex != -1)
            {
                return afterIndex;
            }
            // Nếu không có khoảng trắng nào, trả về giới hạn
            else
            {
                return limit;
            }
        }

        public static Tuple<string, string> SplitStringByBackSpaceMaxCharacters(string input, int maxCharacters)
        {
            int splitIndex = FindBackSpaceNearLimit(input, maxCharacters);

            if (splitIndex == -1)
            {
                return new Tuple<string, string>(input, string.Empty);
            }

            string firstPart = input.Substring(0, splitIndex + 1).Trim(); // +1 to include the comma
            string remainingPart = input.Substring(splitIndex + 1).Trim();

            return new Tuple<string, string>(firstPart, remainingPart);
        }
    }
}