﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Helpers
{
    public static class FileHelper
    {
        public static string ConvertIFormFileToBase64(IFormFile? file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentNullException(nameof(file), "File is null or empty");

            using var ms = new MemoryStream();
            file.CopyTo(ms);
            byte[] fileBytes = ms.ToArray();
            return Convert.ToBase64String(fileBytes);
        }

        public static StreamContent ConvertBase64ToStreamContent(string base64String, string fileName)
        {
            // Chuyển đổi chuỗi Base64 thành mảng byte
            byte[] bytes = Convert.FromBase64String(base64String);

            // Tạo MemoryStream từ mảng byte
            MemoryStream memoryStream = new(bytes);

            // Tạo StreamContent từ MemoryStream
            StreamContent streamContent = new(memoryStream);

            // Đặt tên tệp tin và loại nội dung
            streamContent.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("form-data")
            {
                Name = "file",
                FileName = fileName
            };

            return streamContent;
        }

        public static string CalculateMD5FromBase64(string base64String)
        {
            // Giải mã chuỗi Base64 thành mảng byte
            byte[] data = Convert.FromBase64String(base64String);

            // Tính toán giá trị kiểm tra MD5 từ mảng byte
            byte[] hash = MD5.HashData(data);
            return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
        }

        public static string CalculateSha256Checksum(string base64String)
        {
            byte[] fileBytes = Convert.FromBase64String(base64String);
            byte[] checksumBytes = SHA256.HashData(fileBytes);
            return BitConverter.ToString(checksumBytes).Replace("-", "").ToLower();
        }
    }
}
