﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Extensions
{
    public static class DateTimeExtension
    {
        public static string ToVietNameseShortDate(this DateTime date)
        {
            CultureInfo viVN = new("vi-VN");
            return date.ToString("ddd, d MMM, yyyy", viVN);
        }

        public static string ToVietNameseLongDate(this DateTime date)
        {
            CultureInfo viVN = new("vi-VN");
            return date.ToString("ddd, d MMM, yyyy HH:mm", viVN);
        }
    }
}
