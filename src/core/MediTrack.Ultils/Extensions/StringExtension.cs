﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Extensions
{
    public static class StringExtension
    {
        public static string LowercaseFirstCharacter(this string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var idx = CharacterHelper.GetIndexLastUpcaseCharacter(input);
            if (idx == 0)
            {
                return char.ToLower(input[0]) + input[1..];
            }
            var firstStr = input[..idx];
            var lastStr = input[idx..];
            return firstStr.ToLower() + lastStr;
        }

        public static string? RemoveDiacritics(this string? text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        /// <summary>
        /// Substring with length
        /// </summary>
        /// <param name="str"></param>
        /// <param name="length"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string SubStringExt(this string str, int length, int index = 0)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            if (str.Length <= length + index)
                return str;

            return str[index..(index + length)];
        }

        /// <summary>
        /// Substring end with length
        /// </summary>
        /// <param name="str"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string SubStringEndExt(this string str, int length)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            if (str.Length <= length)
                return str;

            return str[^length..];
        }

        /// <summary>
        /// parse string to int
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static int ToInt(this string? str)
        {
            if (Int32.TryParse(str, out int result))
                return result;
            return 0;
        }
        
        /// <summary>
        /// parse string to int
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static long ToLong(this string? str)
        {
            if (Int64.TryParse(str, out long result))
                
                return result;
            return 0;
        }
    }

    public static class CharacterHelper
    {
        public static int GetIndexLastUpcaseCharacter(string input)
        {
            int index = 0;
            for (int i = 0; i < input.Length - 1; i++)
            {
                var curCh = input[i];
                var nextCh = input[i + 1];
                if (Char.IsUpper(curCh) && Char.IsLower(nextCh))
                    return i;
            }
            return index;
        }
    }
}
