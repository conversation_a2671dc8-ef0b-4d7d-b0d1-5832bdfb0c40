﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Attributes
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true, Inherited = true)]
    public class DataGridAttribute : Attribute
    {
        public DataGridAttribute()
        {
            Visible = true;
        }
        public string? DisplayColumnName { get; set; } = string.Empty;
        public bool Visible { get; set; }
    }
}
