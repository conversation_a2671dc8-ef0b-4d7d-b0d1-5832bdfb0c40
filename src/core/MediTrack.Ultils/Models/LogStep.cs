﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Ultils.Models
{
    public class LogStep
    {
        public LogStep() { }
        public LogStep(string requestId, int stepNumber,
            string stepName, string status, string message, string sourceService = "", string assembly = "")
        {
            RequestId = requestId;
            StepName = stepName;
            StepNumber = stepNumber;
            Status = status;
            Message = message;
            ProcessAt = DateTime.UtcNow;
            SourceService = sourceService;
            Assembly = assembly;
        }
        public string RequestId { get; set; } = string.Empty;
        public string SourceService { get; set; } = string.Empty;
        public int StepNumber { get; set; }
        public string StepName { get; set;} = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Assembly { get; set;} = string.Empty;
        public DateTime ProcessAt { get; set; }
    }
}
