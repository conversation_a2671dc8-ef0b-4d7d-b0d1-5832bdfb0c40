﻿using MediTrack.Application.Features.KioskMetadataLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Helpers
{
    public class HospitalHelper
    {
        public static async Task<List<HealthService>> GetDefaultHealthServices(string hospitalId, string codeMetaData, IDatabaseService databaseService)
        {
            var result = new List<HealthService>();
            var config = await databaseService.HospitalMetaDatas
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.HospitalId == hospitalId
                        && x.GroupType == "default_services"
                        && x.Code == codeMetaData);

            if (config is null || string.IsNullOrEmpty(config.Value))
            {
                return result;
            }

            result = JsonConvert.DeserializeObject<List<HealthService>>(config.Value);
            return result ?? [];
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách dịch vụ y tế đang hoạt động theo thời gian thực.
        /// Hàm này trả về:
        /// - <PERSON><PERSON> sách (id, code) các dịch vụ y tế đang "bật" và trong khung giờ hoạt động.
        /// - Cờ allMetaInactive:
        ///     + true  = Tất cả metadata đều tắt (không có dịch vụ nào active, hoặc active nhưng bị disable hết).
        ///     + false = Có dịch vụ active, nhưng có thể không nằm trong khung giờ hiện tại (result có thể rỗng do không đúng giờ).
        /// </summary>
        public static List<(string id, string code)> GetHealthServiceRealTimeActive(List<HealthServiceKioskMetadataDto> datas)
        {
            var result = new List<(string id, string code)>();
            foreach (var data in datas)
            {
                if (data.Active == false)
                    continue;

                var now = DateTimeHelper.GetCurrentLocalDateTime();
                var hour = now.Hour;
                var dayOfWeek = now.DayOfWeek switch
                {
                    DayOfWeek.Sunday => "Chủ nhật",
                    DayOfWeek.Monday => "Thứ 02",
                    DayOfWeek.Tuesday => "Thứ 03",
                    DayOfWeek.Wednesday => "Thứ 04",
                    DayOfWeek.Thursday => "Thứ 05",
                    DayOfWeek.Friday => "Thứ 06",
                    DayOfWeek.Saturday => "Thứ bảy",
                    _ => "n/a"
                };

                var healthServiceActive = data.HealthServices?.Where(x => x.Active.GetValueOrDefault()).ToList();
                if (healthServiceActive == null || healthServiceActive.Count == 0)
                    continue;
                if (healthServiceActive.Any(x => x.OpenDates == null || x.OpenDates.Count == 0))
                {
                    result.AddRange(healthServiceActive.Select(x => (x.HealthServiceId!, x.HealthServiceCode!)));
                    continue; // Bỏ qua nếu không có OpenDates
                }

                foreach (var item in healthServiceActive)
                {
                    var openDate = item.OpenDates!.FirstOrDefault(x => x.DayOfWeek == dayOfWeek && x.Active.GetValueOrDefault());
                    if (openDate != null)
                    {
                        if (openDate.OpenTimes != null)
                        {
                            foreach (var openTime in openDate.OpenTimes)
                            {
                                if (openTime.IsDisable.GetValueOrDefault())
                                    continue;
                                int timeStart = openTime.TimeStart?.GetTimeByString() ?? 0;
                                int timeEnd = openTime.TimeEnd?.GetTimeByString() ?? 0;
                                if (timeStart == 0 && timeEnd == 0)
                                    continue;

                                int timeCurrent = now.ToString("HH:mm").GetTimeByString();

                                if (timeCurrent >= timeStart && timeCurrent <= timeEnd)
                                {
                                    result.Add((item.HealthServiceId!, item.HealthServiceCode!));
                                }
                            }
                        }
                        else
                        {
                            // Nếu không có OpenTimes, coi như dịch vụ này đang hoạt động cả ngày
                            result.Add((item.HealthServiceId!, item.HealthServiceCode!));
                        }

                    }
                }
            }
            return result;
        }
    }
}