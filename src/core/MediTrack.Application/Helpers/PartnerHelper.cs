﻿using MediTrack.Application.Features.PartnerLogic.Dtos;
using MediTrack.Ultils.Helpers;
using System.Net.NetworkInformation;
using System.Text.RegularExpressions;
using ZXing;
using static System.Net.Mime.MediaTypeNames;

namespace MediTrack.Application.Helpers
{
    public static class PartnerHelper
    {
        public static (bool result, string message) VerifyRequestPayment(CreateQRPaymentQRRequest request)
        {
            if (request.transactionAmount == 0)
                return (false, "Số tiền giao dịch không hợp lệ");
            if (string.IsNullOrEmpty(request.invoiceId))
                return (false, "InvoiceId không được để trống");

            return (true, "");
        }
        public static (bool result, string message) VerifyRequestSearchPayment(PartnerTransactionDecrypt request)
        {
            if (string.IsNullOrEmpty(request.merchantId))
                return (false, "merchantId không được để trống");
            if (request.invoiceIds == null )
                return (false, "<PERSON><PERSON> lòng bổ sung invoiceId");
          

            return (true, "");
        }
        public static string PaymentDecription(string paymentDescription)
        {
            paymentDescription = paymentDescription.RemoveVietnameseCharacter();
            //remove special characters include space, 0-9a-zA-Z, and -, .
            paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
            // limit to 45 characters
            paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
            return paymentDescription;
        }
        /// <summary>
        /// Convert images base 64 to string
        /// </summary>
        /// <param name="base64Image"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        public static string DecodeQRFromBase64(string base64Image)
        {
            try
            {
                if (string.IsNullOrEmpty(base64Image))
                {
                    throw new ArgumentException("Base64 string cannot be null or empty");
                }

                // chuyen base64 thanh byte array 
                byte[] imageBytes = Convert.FromBase64String(base64Image);

                // chuyen byte array thanh image 
                using (var memoryStream = new MemoryStream(imageBytes))
                using (var image = SixLabors.ImageSharp.Image.Load<SixLabors.ImageSharp.PixelFormats.Rgba32>(memoryStream))
                {
                    int width = image.Width;
                    int height = image.Height;

                    //Chuyển Image thành RGB Data
                    byte[] rgb = new byte[width * height * 3];
                    int rgbIndex = 0;

                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width; x++)
                        {
                            var pixel = image[x, y];
                            rgb[rgbIndex] = pixel.R;
                            rgb[rgbIndex + 1] = pixel.G;
                            rgb[rgbIndex + 2] = pixel.B;
                            rgbIndex += 3;
                        }
                    }

                    // 
                    var reader = new BarcodeReaderGeneric<byte[]>
                    {
                        AutoRotate = true,
                        Options = new ZXing.Common.DecodingOptions
                        {
                            TryHarder = true,
                            PossibleFormats = new[] { BarcodeFormat.QR_CODE }
                        }
                    };

                    // chuyen RGB data thanh luminance source vi ZXing nhan vao luminance source 
                    var luminanceSource = new RGBLuminanceSource(rgb, width, height);

                    // Decode 
                    var result = reader.Decode(luminanceSource);
                    return result?.Text ?? "Failed to decode QR content.";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error decoding QR code: {ex.Message}", ex);
            }
        }
    }
}