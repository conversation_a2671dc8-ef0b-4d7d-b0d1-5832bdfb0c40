﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Services
{
    public interface ICurrentUserService
    {
        string UserId { get; set; }
        string UserName { get; set; }
        string IpAddress { get; set; }
        string UserAgent { get; set; }
        string UserHost { get; set; }
    }
}
