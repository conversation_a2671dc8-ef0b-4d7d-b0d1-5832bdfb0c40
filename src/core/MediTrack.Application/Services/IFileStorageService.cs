﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Services
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Uploads a file to S3.
        /// </summary>
        /// <param name="filePath">The local file path.</param>
        /// <param name="bucketName">The S3 bucket name.</param>
        /// <param name="key">The key under which to store the file.</param>
        /// <returns>The URL of the uploaded file.</returns>
        Task<(bool,string)> UploadFileAsync(string filePath, 
            string bucketName, string key);

        /// <summary>
        /// Uploads a file to S3.
        /// </summary>
        /// <param name="base64">File as base64</param>
        /// <param name="bucketName">The S3 bucket name.</param>
        /// <param name="key">The key under which to store the file.</param>
        /// <returns>The URL of the uploaded file.</returns>
        Task<(bool, string, string)> UploadFileAsBase64Async(string base64,
            string bucketName, string key);
    }
}
