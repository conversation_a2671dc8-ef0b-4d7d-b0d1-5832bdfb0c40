﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Services
{
    public interface IMailService
    {
        public Task SendViaFluentAttach(string subject, string body, Stream data, string filename, string mailFrom, string mailTo, string mailCC, string mailBCC);
        public Task SendViaFluent(string subject, string body, string mailFrom, string mailTo, string mailCC, string mailBCC);
    }
}
