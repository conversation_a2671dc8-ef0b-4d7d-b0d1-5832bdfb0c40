using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Services
{
    public interface IDatabaseService
    {
        public DbSet<Hospital> Hospitals { get; }
        public DbSet<Customer> Customers { get; }
        public DbSet<Clinic> Clinics { get; }
        public DbSet<HealthService> HealthServices { get; }
        public DbSet<Receipt> Receipts { get; }
        public DbSet<Register> Registers { get; }
        public DbSet<Payment> Payments { get; }
        public DbSet<ServiceClient> ServiceClients { get; }
        public DbSet<RefreshToken> RefreshTokens { get; }
        public DbSet<Province> Provinces { get; }
        public DbSet<District> Districts { get; }
        public DbSet<Ward> Wards { get; }
        public DbSet<User> Users { get; }
        public DbSet<ObjectPermission> ObjectPermissions { get; }
        public DbSet<Nationality> Nationalities { get; }
        public DbSet<Nation> Nations { get; }
        public DbSet<Kiosk> Kiosks { get; }
        public DbSet<AppHome> AppHomes { get; }
        public DbSet<AppBanner> AppBanners { get; }
        public DbSet<HospitalMetaData> HospitalMetaDatas { get; }
        public DbSet<Career> Careers { get; }
        public DbSet<Otp> Otps { get; }
        public DbSet<DialerQueue> DialerQueues { get; }
        public DbSet<Relationship> Relationships { get; }
        public DbSet<CustomerRelationShip> CustomerRelationShips { get; }
        public DbSet<CustomerHospital> CustomerHospitals { get; }
        public DbSet<His> His { get; }
        public DbSet<HisMetaData> HisMetaDatas { get; }
        public DbSet<SystemMetaData> SystemMetaDatas { get; }
        public DbSet<BankBranch> BankBranches { get; set; }
        public DbSet<ForgotPasswordSession> ForgotPasswordSessions { get; set; }
        public DbSet<HospitalUser> HospitalUsers { get; set; }
        public DbSet<KioskMetaData> KioskMetaDatas { get; set; }
        public DbSet<HealthServiceMetaData> HealthServiceMetaDatas { get; set; }
        public DbSet<RegisterDetail> RegisterDetails { get; set; }
        public DbSet<KioskReleaseHistory> KioskReleaseHistories { get; set; }
        public DbSet<KioskStatusLog> KioskStatusLogs { get; set; }
        public DbSet<CustomerReview> CustomerReviews { get; set; }
        public DbSet<PatientBooking> PatientBookings { get; set; }
        public DbSet<AdvertisingPartner> AdvertisingPartners { get; set; }
        public DbSet<AdvertisingPartnerMetaData> AdvertisingPartnerMetaDatas { get; set; }
        public DbSet<AdvertisingCampaign> AdvertisingCampaigns { get; set; }
        public DbSet<AdvertisingPartnerCampaign> AdvertisingPartnerCampaigns { get; set; }
        public DbSet<Membership> Memberships { get; set; }
        public DbSet<PackageService> PackageServices { get; set; }
        public DbSet<HealthPackageDetail> HealthPackageDetails { get; set; }
        public DbSet<ExameType> ExameTypes { get; set; }
        public DbSet<HospitalNotification> HospitalNotifications { get; set; }
        public DbSet<AdvertisingKioskCampaign> CampaignParticipations { get; set; }
        public DbSet<UserShareInfo> UserShareInfors { get; set; }
        public DbSet<SessionMembershipConfirm> SessionMembershipConfirms { get; set; }
        public DbSet<CustomerFlow> CustomerFlows { get; set; }
        public DbSet<DonationCampaign> DonationCampaigns { get; set; }
        public DbSet<DonationFollower> DonationFollowers { get; set; }
        public DbSet<DonationHistory> DonationHistories { get; set; }
        public DbSet<DonationCampaignType> DonationCampaignTypes { get; set; }
        public DbSet<DialerQueueHistory> DialerQueueHistories { get; set; }
        public DbSet<MembershipHistory> MembershipHistories { get; set; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken);

        int SaveChanges();
    }
}