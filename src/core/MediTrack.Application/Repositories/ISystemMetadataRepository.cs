﻿using MediTrack.Application.Features.SystemMetadataLogic.Dtos;

namespace MediTrack.Application.Repositories
{
    public interface ISystemMetadataRepository
    {
        Task<SystemMetadataDto?> GetSystemMetadataByKeyAsync(
            string? groupType = null,
            string? code = null,
            CancellationToken cancellationToken = default);

        Task<List<SystemMetadataDto>> GetSystemMetadatasAsync(CancellationToken cancellationToken = default);
    }
}