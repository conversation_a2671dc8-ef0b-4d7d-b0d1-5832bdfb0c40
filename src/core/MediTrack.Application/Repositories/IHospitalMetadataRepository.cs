﻿using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;

namespace MediTrack.Application.Repositories
{
    public interface IHospitalMetadataRepository
    {
        Task<HospitalMetadataDto?> GetHospitalMetadataByKeyAsync(
            string? hospitalId,
            string? groupType = null,
            string? code = null,
            CancellationToken cancellationToken = default);

        Task<List<HospitalMetadataDto>> GetHospitalMetadatasByHospitalAsync(string? hospitalId, CancellationToken cancellationToken = default);

        //value structure
        // [
        //   "xxxxx": {
        //     "HealthcareServiceTierId": "1.1",
        //     "MedicalTreatmentCategoryId": "1",
        //     },
        //   "xx*": {
        //     "ExcludeHospitalIds": ["xxxxx","xxxxx","xxxxx","xxxxx"]",
        //     "HealthcareServiceTierId": "1.2",
        //     "MedicalTreatmentCategoryId": "2",
        //     },
        //   "*": {
        //     "HealthcareServiceTierId": "",
        //     "MedicalTreatmentCategoryId": "",
        //     },
        // ]
        Task<HospitalMetadataInsuranceDataConfigDefaultDto?> GetHospitalMetadataInsuranceDataConfigDefaultAsync(
            string hospitalId,
            string primaryCareRegistrationSite,
            CancellationToken cancellationToken = default);

        Task<Dictionary<string, string>> GetInsuranceErrorMessageConfig(
            string hospitalId, CancellationToken cancellationToken = default);
    }
}