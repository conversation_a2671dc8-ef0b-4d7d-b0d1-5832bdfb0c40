﻿using HDBankClient.Lib.Config;
using HDBankClient.Lib.Request;
using HDBankClient.Lib.Response;

namespace MediTrack.Application.Integrate
{
    public interface IBankService
    {
        // Task<(bool result, string responseCode, string message, CheckAccountResponse? resData)> HdBankCheckAccount(CheckAccountRequest request, string requestId);
        // Task<(bool, string, string)> HdBankUpdateCustomerInfo(UpdateCustomerInfoRequest request, string requestId);
        // Task<(bool, string, string)> HdBankConfirmOtp(ConfirmOtpRequest request, string requestId);
        // Task<(bool, string, string)> HdBankSendOtpToNewCustomer(SendOtpRequest request, string requestId);
        // Task<(bool, string, string, CheckStatusResponse?)> HdBankCheckStatus(CheckStatusRequest request, string requestId);
    }
}
