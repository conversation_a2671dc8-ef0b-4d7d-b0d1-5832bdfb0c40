
using MediBankClient.Lib.Base;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;

namespace MediTrack.Application.Integrate
{
    public interface IMediBankService
    {
        Task<BaseResponseModel<CheckAccountResponse>> CheckHdBankAccount(CheckAccountRequest request);
        Task<BaseResponseModel<object>> UpdateCustomerInfo(UpdateCustomerInfoRequest request);
        Task<BaseResponseModel<object>> SendOtpToNewCustomer(SendOtpToNewCustomerRequest request);
        Task<BaseResponseModel<object>> ConfirmOtp(ConfirmHdBankOtpRequest request);
        Task<BaseResponseModel<CheckStatusOnBoardingResponse>> CheckStatusOnBoarding(CheckStatusOnBoardingRequest request);
        Task<BaseResponseModel<UploadIdentityToHdBankResponse>> UploadIdentityToHdBank(UploadIdentityToHdBankRequest request);
        Task<BaseResponseModel<UploadFileResponse>> UploadFile(UploadFileRequest request);
        Task<BaseResponseModel<CreatePaymentResponse>> CreateQrTransaction(CreateHdBankQrPayment request);
        Task<BaseResponseModel<CreateTransferResponse>> CreateTransfer(CreateTransferRequest request);
        Task<BaseResponseModel<AvailableBalanceResponse>> GetAvailableBalance(GetHdBankAvailableBalanceRequest request);
        Task<BaseResponseModel<CreateLoginQrResponse>> CreateLoginQr(CreateLoginQrRequest request);
        Task<BaseResponseModel<object>> CheckLoginQrStatus(CheckLoginQrStatusRequest request);
        Task<BaseResponseModel<List<HdBankAccountResponse>>> GetAllAccount(GetAllAccountHdBankRequest request);
        Task<BaseResponseModel<object>> GetStatusAccountLink(GetStatusAccountLinkRequest request);
        Task<BaseResponseModel<List<CareerResponse>>> GetCarees();
    }
}
