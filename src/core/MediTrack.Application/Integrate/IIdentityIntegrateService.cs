﻿using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;

namespace MediTrack.Application.Integrate
{
    public interface IIdentityIntegrateService
    {
        Task<BaseResponseModel<GetUrlSSOVneidResponse>> VneidGetUrlSSO();

        Task<BaseResponseModel<GetUserByTokenSSOVneidResponse>> VneidGetUserSSO(GetUserByTokenSSOVneidRequest request);

        Task<BaseResponseModel<LeeoneIDVerificationResponse>> VerifyEID(LeeoneIDVerificationRequest request);

        Task<BaseResponseModel<LivenessVerificationResponse>> VerifyLiveness(LeeonLivenessVerificationRequest request);

        Task<BaseResponseModel<LeeonFaceMatchingVerificationResponse>> FaceMatching(LeeonFaceMatchingVerificationRequest request);

        Task<BaseResponseModel<AddFaceResponse>> AddFace(AddFaceRequest request);
            
        Task<BaseResponseModel<FindFaceResponse>> FindFace(FindFaceRequest request);
    }
}