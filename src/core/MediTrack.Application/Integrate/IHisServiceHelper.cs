﻿using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;

namespace MediTrack.Application.Integrate
{
    public interface IHisServiceHelper
    {
        /// <summary>
        /// Create His Service
        /// </summary>
        /// <param name="currentHospital"></param>
        /// <param name="databaseService"></param>
        /// <param name="logPrefix"></param>
        /// <returns></returns>
        IHisService CreateHisService(CurrentHospitalDto currentHospital,
            IHttpClientFactory httpClientFactory,
            IDatabaseService databaseService,
            ICachedService cachedService,
            string logPrefix,
            IHospitalMetadataRepository? hospitalMetadataRepository = null,
            IProvinceRepository? provinceRepository = null,
            IDistrictRepository? districtRepository = null,
            IWardRepository? wardRepository = null);
        
        /// <summary>
        /// Get Hospital Service By Id
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        CurrentHospitalDto GetHospitalServiceById(string hospitalId, IDatabaseService databaseService);

        /// <summary>
        /// Get Hospital
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task<Hospital?> GetHospital(string hospitalId, IDatabaseService databaseService, CancellationToken cancellationToken = default);

        /// <summary>
        /// Reset Hospital Cache
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task ResetHospitalCache(string hospitalId);

        /// <summary>
        /// Get Kiosk
        /// </summary>
        /// <param name="kioskId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task<Kiosk?> GetKiosk(string kioskId, IDatabaseService databaseService, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Kiosk
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task<List<Kiosk>?> GetKiosks(string hospitalId, IDatabaseService databaseService);

        /// <summary>
        /// Reset Kiosk Cache
        /// </summary>
        /// <param name="kioskId"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task ResetKioskCache(string kioskId);

        /// <summary>
        /// Reset Kiosk Cache
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <returns></returns>
        Task ResetKiosksCache(string hospitalId);
    }
}
