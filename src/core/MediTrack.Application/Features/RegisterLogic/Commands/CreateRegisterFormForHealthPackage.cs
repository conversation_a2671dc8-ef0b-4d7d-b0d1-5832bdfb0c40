using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthPackageLogic.Queries;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using PaymentClient.Lib.Response;
using Serilog;
using System.Text.RegularExpressions;

namespace MediTrack.Application.Features.RegisterLogic.Commands
{
    public class CreateRegisterFormForHealthPackage : IRequest<BaseCommandResultWithData<CreateRegisterFormForHealthPackageResponseDto>>
    {
        public string CustomerKey { get; set; } = string.Empty;
        public string CustomerRelationKey { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        public string HealthPackageId { get; set; } = string.Empty;
        public string HealthPackageTypeId { get; set; } = string.Empty;
        public List<TechnicalService>? TechnicalServices { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }
        public string? PatientId { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public string? PaymentType { get; set; }
        public string? ReasonForVisit { get; set; }
        public string? EducationLevel { get; set; }
        public int? Priority { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkAddress { get; set; }
        public string? ProvinceId { get; set; }
        public string? WardId { get; set; }
        public string? DistrictId { get; set; }
        public string? Street { get; set; }
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class CreateRegisterFormForHealthPackageHandler(ICurrentUserService currentUserService,
        IMediator mediator,
        IDatabaseService databaseService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IOptions<PaymentConfig> paymentConfig,
        ICurrentHospitalService currentHospitalService,
        IHisServiceHelper hisServiceHelper,
        IHttpClientFactory httpClientFactory) : IRequestHandler<CreateRegisterFormForHealthPackage, BaseCommandResultWithData<CreateRegisterFormForHealthPackageResponseDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseCommandResultWithData<CreateRegisterFormForHealthPackageResponseDto>> Handle(
            CreateRegisterFormForHealthPackage request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateRegisterFormForHealthPackageResponseDto>();
            Log.Information("{LogPrefix} Request: {@Request}", currentHospitalService.LogPrefix, request);

            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                var customer = await databaseService.Customers
                    .FindAsync(request.CustomerKey, cancellationToken);
                Log.Information("{LogPrefix} Customer Id: {Id}", currentHospitalService.LogPrefix, customer?.Id);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.InvalidCustomer, ErrorTypeEnum.MediPayError);
                    return result;
                }

                var customerRelationship = new Customer();
                string customerRelationshipName = string.Empty;
                string customerRelationshipId = string.Empty;
                if (!string.IsNullOrEmpty(request.CustomerRelationKey))
                {
                    customerRelationship = await databaseService.Customers
                            .FindAsync([request.CustomerRelationKey], cancellationToken);
                    if (customerRelationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelation, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var customerRelation = await databaseService.CustomerRelationShips
                        .Include(a => a.Relationship)
                        .FirstOrDefaultAsync(a => a.CustomerId.Equals(request.CustomerRelationKey) && a.CustomerIdRefer.Equals(request.CustomerKey), cancellationToken);

                    if (customerRelation is null || customerRelation.Relationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelationship, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    customerRelationshipName = customerRelation.Relationship.Name;
                    customerRelationshipId = customerRelation.Relationship.Id;
                }

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customer.CareerId != request.CareerId)
                {
                    customer.CareerId = request.CareerId ?? string.Empty;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }


                if (!string.IsNullOrEmpty(request.EducationLevel)
                    && customer.EducationLevel != request.EducationLevel)
                {
                    customer.EducationLevel = request.EducationLevel;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkPlace)
                    && customer.WorkPlace != request.WorkPlace)
                {
                    customer.WorkPlace = request.WorkPlace;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkAddress)
                    && customer.WorkAddress != request.WorkAddress)
                {
                    customer.WorkAddress = request.WorkAddress;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if ((!string.IsNullOrEmpty(request.ProvinceId) && customer.ProvinceId != request.ProvinceId) ||
                    (!string.IsNullOrEmpty(request.WardId) && customer.WardId != request.WardId) ||
                    (!string.IsNullOrEmpty(request.DistrictId) && customer.DistrictId != request.DistrictId) ||
                    (!string.IsNullOrEmpty(request.Street) && customer.Street != request.Street))
                {
                    var isRequestTwoLevelAddress = request.IsTwoLevelAddress.GetValueOrDefault();

                    var ward = await wardRepository.GetWardByIdAsync(request.WardId!, isRequestTwoLevelAddress, cancellationToken);

                    string? districtName = null;
                    if (!isRequestTwoLevelAddress)
                    {
                        districtName = (await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId, cancellationToken)).FirstOrDefault(p => p.Id == request.DistrictId)?.Name;
                    }

                    var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);
                    var province = provinces.FirstOrDefault(p => (isRequestTwoLevelAddress ? p.NewId : p.Id) == request.ProvinceId);

                    if (ward != null && province != null && (isRequestTwoLevelAddress || districtName != null))
                    {
                        customer.IsSyncedTwoLevelAddress = isRequestTwoLevelAddress;

                        if (isRequestTwoLevelAddress)
                        {
                            customer.ProvinceId = province?.NewId;
                            customer.DistrictId = string.Empty;
                            customer.WardId = ward?.NewId;
                            customer.Address = $"{customer.Street}, {ward?.NewName}, {province?.NewName}";
                        }
                        else
                        {
                            customer.ProvinceId = request.ProvinceId;
                            customer.DistrictId = request.DistrictId;
                            customer.WardId = request.WardId;
                            customer.Address = $"{customer.Street}, {ward?.Name}, {districtName}, {province?.Name}";
                        }

                        customer.Street = request.Street;

                        customer.SetUpdate(currentUserService.UserName);
                        databaseService.Customers.Update(customer);
                    }
                }

                var customerHospital = await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.CustomerId == customer.Id && x.HospitalId == request.HospitalId, cancellationToken);

                var isNotFoundCustomerHospital = customerHospital == null;
                if (isNotFoundCustomerHospital)
                {
                    customerHospital = new CustomerHospital { HospitalId = request.HospitalId, CustomerId = customer.Id };
                    customerHospital.SetCreate(currentUserService.UserName);
                }
                else
                {
                    customerHospital!.SetUpdate(currentUserService.UserName);
                }

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customerHospital.CareerId != request.SocialCareerId)
                {
                    customerHospital.CareerId = request.SocialCareerId ?? string.Empty;
                }

                bool getResult = false;
                string message = string.Empty;

                // 0. Nếu khách hàng chưa có mã bệnh nhân thì gọi sang HIS tích hợp mã bệnh nhân
                if (string.IsNullOrEmpty(customerHospital.PatientCode))
                {
                    if (!string.IsNullOrEmpty(request.PatientId) && !string.IsNullOrEmpty(request.PatientCode))
                    {
                        customerHospital.PatientId = request.PatientId;
                        customerHospital.PatientCode = request.PatientCode;
                    }
                    else
                    {
                        (getResult, message, errorType, HisCustomerDto hisCustomerDto) = await hisService.GetCustomerHis(customer);
                        Log.Information("{LogPrefix} GetPationCode from HIS: {getResult} - - - message: {message} - - - patientCode: {patientCode} ", currentHospitalService.LogPrefix, getResult, message, hisCustomerDto.PatientCode);
                        if (getResult && !string.IsNullOrEmpty(hisCustomerDto.PatientCode))
                        {
                            //1. Save patient code
                            customerHospital.PatientId = hisCustomerDto.PatientId;
                            customerHospital.PatientCode = hisCustomerDto.PatientCode;
                        }
                        // Logic mới: Nếu HIS không trả về mã BN thì để trống (PatientCode giữ nguyên giá trị mặc định empty)
                    }
                }

                // 1. Tạo và lưu phiếu tiếp nhận
                List<HealthPackage>? healthPackages = [];
                HealthPackage? package = null;
                Register register = new();

                var packages = await mediator.Send(new GetHealthPackages
                {
                    HealthPackageTypeId = request.HealthPackageTypeId,
                }, cancellationToken);

                if (packages.Data == null)
                {
                    Log.Error("{LogPrefix} GetHealthPackages: Health Packages is null", currentHospitalService.LogPrefix);
                    result.Set(false, "Không thể lấy danh sách gói khám", ErrorTypeEnum.MediPayError);
                    return result;
                }
                getResult = packages.Success;
                message = packages.Messages;
                healthPackages = packages.Data;


                Log.Information("{LogPrefix} Get HealthPackage getResult: {getResult} - - - message: {message} - - - HealthServices: {@HealthServices} ", currentHospitalService.LogPrefix, getResult, message, healthPackages);
                if (healthPackages == null || healthPackages.Count == 0)
                {
                    result.Set(false, "Không thể lấy danh sách gói khám", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không thể lấy danh sách gói khám", currentHospitalService.LogPrefix);
                    return result;
                }

                package = healthPackages.FirstOrDefault(x => x.Id == request.HealthPackageId
                    && (string.IsNullOrEmpty(request.HealthPackageTypeId) || x.HealthPackageTypeId == request.HealthPackageTypeId));

                if (package is null)
                {
                    result.Set(false, $"Không tìm thấy gói khám '{request.HealthPackageId}'", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không tìm thấy gói khám '{HealthPackageId}' - Danh sách: {@Healthpackages}", currentHospitalService.LogPrefix, request.HealthPackageId, healthPackages);
                    return result;
                }

                // get health package type
                var healthPackageTypes = await mediator.Send(new GetHealthPackageTypes(), cancellationToken);
                if (!healthPackageTypes.Success)
                {
                    result.Set(false, $"Không tìm thấy danh sách loại gói khám", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không tìm thấy loại khám '{HealthPackageTypeId}' - Danh sách: {@HealthPackageTypes}", currentHospitalService.LogPrefix, request.HealthPackageTypeId, healthPackageTypes.Data);
                    return result;
                }

                var healthPackageType = healthPackageTypes.Data?.FirstOrDefault(x => x.Id == request.HealthPackageTypeId);
                if (healthPackageType is null)
                {
                    result.Set(false, $"Không tìm thấy loại gói khám '{request.HealthPackageTypeId}'", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không tìm thấy loại khám '{HealthPackageTypeId}' - Danh sách: {@HealthPackageTypes}", currentHospitalService.LogPrefix, request.HealthPackageTypeId, healthPackageTypes.Data);
                    return result;
                }

                decimal unitPrice = package.UnitPrice;

                register.CustomerId = customer.Id;
                register.CustomerRelationId = customerRelationship.Id;
                register.Number = IdentityHelper.Guid(15);
                register.HealthServiceId = package.Id;
                var registerAt = DateTimeHelper.GetCurrentLocalDateTime();
                register.RegisterAt = registerAt.AddHours(-7);
                register.SetCreate(currentUserService.UserName);
                register.SubTotalAmount = unitPrice;
                register.TotalAmount = unitPrice;
                register.HospitalId = request.HospitalId;
                register.DeviceId = currentHospitalService.KioskId;
                register.ExameTypeId = request.HealthPackageId;
                register.ExameType = healthPackageType?.Name ?? string.Empty;

                register.ReasonForVisit = request.ReasonForVisit ?? string.Empty;


                (bool createResult, string createMessage, errorType, RegisterFormPackageResponseDto resRegisterFrom)
                    = await hisService.CreateRegisterFormforHealthPackage(new RegisterFormPackageRequestDto
                    {
                        Customer = customer,
                        CustomerHospital = customerHospital,
                        HealthPackage = package,
                        TechnicalServices = request.TechnicalServices ?? [],
                        Priority = request.Priority ?? 0,
                    });

                string registerNo = resRegisterFrom.RegisterNumber;
                register.IsHisCreateFormSuccess = createResult;
                register.HisMessageWhenCreateForm = createMessage;

                Log.Information("{LogPrefix} CreateRegisterFormforHealthPackage createResult: {createResult} - - - message: {createMessage} - - - registerNo: {registerNo} ", currentHospitalService.LogPrefix, createResult, createMessage, registerNo);
                if (!createResult)
                {
                    result.Set(false, createMessage, ErrorTypeEnum.HisError);
                    return result;
                }
                decimal totalAmount = unitPrice + request.TechnicalServices?.Sum(x => x.UnitPrice * x.Quantity) ?? 0;

                if (resRegisterFrom != null)
                {
                    register.RefDocNo = resRegisterFrom.RefDocNo;
                    register.PatientCode = resRegisterFrom.PatientCode;
                    register.QueueNumber = resRegisterFrom.QueueNumber;

                    var service = package.HealthPackageServices.Select(x => new RegisterDetail
                    {
                        Id = IdentityHelper.Guid(15),
                        RegisterNumber = register.Number,
                        HealthServiceId = package.Id,
                        ServiceCode = x.Id,
                        ServiceName = x.Name ?? string.Empty,
                        UnitPrice = x.UnitPrice,
                        Quantity = 1,
                        CreatedAt = DateTime.UtcNow
                    }).ToList();
                    service.AddRange(request.TechnicalServices?.Select(x => new RegisterDetail
                    {
                        Id = IdentityHelper.Guid(15),
                        RegisterNumber = register.Number,
                        HealthServiceId = "Technical",
                        ServiceCode = x.Id,
                        ServiceName = x.Name,
                        UnitPrice = x.UnitPrice,
                        Quantity = x.Quantity,
                        CreatedAt = DateTime.UtcNow,
                    }).ToList() ?? []);
                    databaseService.RegisterDetails.AddRange(service);

                    var receipt = new Receipt
                    {
                        Number = IdentityHelper.Guid(15),
                        CustomerId = customer.Id,
                        TotalAmount = totalAmount,
                        ReceiptDate = DateTime.UtcNow,
                        RegisterNumber = register.Number,
                        Status = unitPrice > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                        DeviceId = currentHospitalService.KioskId,
                        HospitalId = register.HospitalId,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.ReceiptRefNo,
                        CreatedAt = DateTime.UtcNow
                    };

                    var payment = new Payment
                    {
                        Id = receipt.Number,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.PaymentRefNo,
                        PaymentDate = DateTime.UtcNow,
                        PaymentAmount = receipt.TotalAmount,
                        ReceiptNumber = receipt.Number,
                        CreatedAt = DateTime.UtcNow,
                        Status = unitPrice > 0 ? PaymentConstant.WaitForPayment : PaymentConstant.Success,
                        InvoiceInfoRef = resRegisterFrom.RefDocNo,
                        IsHisGenQr = currentHospitalService.CurrentHospital.IsGenQR,
                        HospitalId = register.HospitalId
                    };

                    //tạo QR code khi tạo phiếu đăng ký nếu cấu hình bệnh viện cho phép
                    if (currentHospitalService.CurrentHospital.IsGenQRWhenCreateRegister && unitPrice > 0)
                    {
                        //3. Call API Gen Qr
                        var config = new PaymentConfigModel()
                        {
                            Url = paymentConfig.PaymentUrl,
                            SecretKey = paymentConfig.SecretKey
                        };

                        //get hospital
                        var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
                        if (hospital == null)
                        {
                            result.Set(false, "Không tìm thấy thông tin bệnh viện", ErrorTypeEnum.MediPayError);
                            return result;
                        }

                        string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;

                        //remove special characters include space, 0-9a-zA-Z, and -, .
                        paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
                        // limit to 45 characters
                        paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
                        var paymentRequest = new CreateQrRequest()
                        {
                            MerchantId = hospital.MerchantId,
                            InvoiceId = payment.Id,
                            Type = PaymentConstant.DefaultType,
                            TransactionAmount = (double)payment.PaymentAmount,
                            Ipn = paymentConfig.IpnUrl,
                            TransactionDescription = $"THANH TOAN PHIEU THU {paymentDescription}",
                        };

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

                        (bool processResult, string processMessage, CreateQrResponse? res) = await
                            PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                            currentHospitalService.LogPrefix, processResult, processMessage, res);

                        config.Dispose();

                        if (processResult)
                        {
                            payment.QrCode = res!.QrCode;
                            payment.RefNo = res!.PaymentCode;
                            receipt.QrCode = payment.QrCode;
                            payment.RefDescReq = res!.TransactionDescription;
                        }
                    }

                    databaseService.Receipts.Add(receipt);
                    databaseService.Payments.Add(payment);

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientCode)
                        && resRegisterFrom.PatientCode != customerHospital.PatientCode)
                    {
                        customerHospital.PatientCode = resRegisterFrom.PatientCode;
                    }

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientId)
                        && resRegisterFrom.PatientId != customerHospital.PatientId)
                    {
                        customerHospital.PatientId = resRegisterFrom.PatientId;
                    }
                }

                // Save to database
                if (isNotFoundCustomerHospital)
                {
                    databaseService.CustomerHospitals.Add(customerHospital);
                }
                else
                {
                    databaseService.CustomerHospitals.Update(customerHospital);
                }

                register.ServiceName = package?.Name ?? string.Empty;
                register.RegisterType = "GOI_KHAM";
                register.RefNo = registerNo;
                databaseService.Registers.Add(register);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);
                Log.Information("{LogPrefix} saveResult: {saveResult} ", currentHospitalService.LogPrefix, saveResult);
                if (saveResult > 0)
                {
                    result.Set(true, RegisterConstant.SaveChangesSuccess, new CreateRegisterFormForHealthPackageResponseDto
                    {
                        Number = register.Number,
                        RefNo = register.RefNo,
                        HealthInsurance = 0,
                        UnitPrice = unitPrice,
                        PatientCode = customerHospital.PatientCode ?? string.Empty
                    });
                }
                else
                {
                    result.Set(false, RegisterConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Có lỗi xảy ra khi tạo phiếu tiếp nhận", currentHospitalService.LogPrefix);
                result.Set(false, RegisterConstant.CreateError, errorType);
            }

            return result;
        }
    }
}