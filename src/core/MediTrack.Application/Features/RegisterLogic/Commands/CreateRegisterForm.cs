using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.ClinicLogic.Queries;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.ExameTypeLogic.Queries;
using MediTrack.Application.Features.HealthServiceLogic.Queries;
using MediTrack.Application.Features.HospitalLogic.Queries;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using PaymentClient.Lib.Response;
using Serilog;
using System.Text.RegularExpressions;

namespace MediTrack.Application.Features.RegisterLogic.Commands
{
    public class CreateRegisterForm : RegisterFormDto, IRequest<BaseCommandResultWithData<CreateRegisterFormResponseDto>>
    {
        public string? MembershipId { get; set; }
        public string MaPhong { get; set; } = string.Empty;
        public int UuTien { get; set; } = 1;
        public string ExameTypeId { get; set; } = string.Empty;
        public string ClinicId { get; set; } = string.Empty;
        public string SubClinicId { get; set; } = string.Empty;
        public string ClinicCode { get; set; } = string.Empty;
        public string ClinicName { get; set; } = string.Empty;
        public string ClinicGroupId { get; set; } = string.Empty;
        public string ClinicGroupCode { get; set; } = string.Empty;
        public bool IsInsurance { get; set; }
        public CustomerHealthInsurance? Insurance { get; set; }
        public string? RefRegisterNumber { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }

        /// <summary>
        /// Tuyến khám bệnh: Đúng tuyến, trái tuyến, Đa tuyến đúng tuyến
        /// </summary>
        public string? HealthcareServiceTierId { get; set; }

        /// <summary>
        /// Loại hình khám bệnh: Khám bệnh, ngoại trú, Nội trú....
        /// </summary>
        public string? HealthcareServiceTypeId { get; set; }

        public string? MedicalTreatmentCategoryId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh HIS: Bảo hiểm, tự nguyện, viện phí,..
        /// </summary>
        public string? MedicalTreatmentCategoryHisId { get; set; }

        /// <summary>
        /// Lý do khám bệnh
        /// </summary>
        public string? ReasonForVisit { get; set; }

        public string? EducationLevel { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkAddress { get; set; }

        /// <summary>
        /// Số giấy chuyển tuyến
        /// </summary>
        public string? TransferReferralDocumentNumber { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến
        /// </summary>
        public string? TransferReferralDiseaseCode { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến (Kèm tên bệnh)
        /// </summary>
        public string? TransferReferralDiseaseCodeAndName { get; set; }

        /// <summary>
        /// Đơn vị chuyển tuyến
        /// </summary>
        public string? TransferReferralUnit { get; set; }

        /// <summary>
        /// Hình thức chuyển tuyến
        /// </summary>
        public string? TransferReferralType { get; set; }

        /// <summary>
        /// Lý do chuyển tuyến
        /// </summary>
        public string? TransferReferralReason { get; set; }

        /// <summary>
        /// Ngày chuyển tuyến
        /// </summary>
        public string? TransferReferralDate { get; set; }

        /// <summary>
        /// Thông tin chuẩn đoán tuyến dưới
        /// </summary>
        public string? TransferReferralDiagnosisInfo { get; set; }

        public string? ProvinceId { get; set; }
        public string? WardId { get; set; }
        public string? DistrictId { get; set; }
        public string? Street { get; set; }

        //Chỉ số sinh tồn
        public string? BloodPressure { get; set; } = string.Empty;

        public int? HeartRate { get; set; }
        public int? RespiratoryRate { get; set; }
        public double? BloodOxygen { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public int? PulseRate { get; set; }
        public double? Temperature { get; set; }

        //Loại hình thanh toán: Tiền mặt (cash), thẻ (card), chuyển khoản (qr), miễn phí (free)...truy
        public string? PaymentType { get; set; }

        //Có điều trị arv không
        public bool IsOnARVTreatment { get; set; }

        public string? PatientId { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;
        public string? RegisterType { get; set; }
        public string? PatientBookingNumber { get; set; }
        public string? DiagnosisBeforeAdmission { get; set; }
        public string? DiagnosisBeforeAdmissionICD { get; set; }
        public bool? IsTwoLevelAddress { get; set; } = false;
        public string? AccidentCode { get; set; }
        public string? BloodType { get; set; }
    }

    public class CreateRegisterFormHandler(ICurrentUserService currentUserService,
        IMediator mediator,
        ICachedService cachedService,
        IDatabaseService databaseService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IOptions<PaymentConfig> paymentConfig,
        ICurrentHospitalService currentHospitalService,
        IHisServiceHelper hisServiceHelper,
        IHttpClientFactory httpClientFactory) : IRequestHandler<CreateRegisterForm, BaseCommandResultWithData<CreateRegisterFormResponseDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseCommandResultWithData<CreateRegisterFormResponseDto>> Handle(
            CreateRegisterForm request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateRegisterFormResponseDto>();
            Log.Information("{LogPrefix} Request: {@Request}", currentHospitalService.LogPrefix, request);
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                //Tái khám
                if (!string.IsNullOrEmpty(request.RefRegisterNumber))
                {
                    var getRegisterOld = await databaseService.Registers.FirstOrDefaultAsync(x => x.Number == request.RefRegisterNumber, cancellationToken);
                    if (getRegisterOld == null)
                    {
                        Log.Error("{LogPrefix} GetRegisterOld: {@Request}", currentHospitalService.LogPrefix, request);
                        result.Set(false, "Không tìm thấy phiếu tiếp nhận cũ", ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    request.CustomerKey = getRegisterOld.CustomerId ?? string.Empty;
                    request.CustomerRelationKey = getRegisterOld.CustomerRelationId ?? string.Empty;
                    request.HealthServiceId = getRegisterOld.HealthServiceId ?? string.Empty;
                    request.ServiceCode = getRegisterOld.ServiceCode ?? string.Empty;
                    request.ExameTypeId = getRegisterOld.ExameTypeId ?? string.Empty;
                    request.MaPhong = getRegisterOld.ClinicId;
                    request.ClinicId = getRegisterOld.ClinicId;
                    request.ClinicCode = getRegisterOld.ClinicCode;
                    request.SubClinicId = getRegisterOld.SubClinicId;
                    request.ClinicGroupId = getRegisterOld.ClinicGroupId;
                    request.ClinicGroupCode = getRegisterOld.ClinicGroupCode;
                    request.HealthcareServiceTierId = getRegisterOld.HealthcareServiceTierId;
                    request.HealthcareServiceTypeId = getRegisterOld.HealthcareServiceTypeId;
                    request.MedicalTreatmentCategoryId = getRegisterOld.MedicalTreatmentCategoryId ?? string.Empty;
                    request.MedicalTreatmentCategoryHisId = getRegisterOld.MedicalTreatmentCategoryHisId ?? string.Empty;
                    request.ReasonForVisit = getRegisterOld.ReasonForVisit;
                }

                var customer = await databaseService.Customers
                    .FindAsync([request.CustomerKey], cancellationToken);
                Log.Information("{LogPrefix} Customer Id: {Id}", currentHospitalService.LogPrefix, customer?.Id);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.InvalidCustomer, ErrorTypeEnum.MediPayError);
                    return result;
                }

                var customerRelationship = new Customer();
                string customerRelationshipName = "";
                string customerRelationshipId = "";
                if (!string.IsNullOrEmpty(request.CustomerRelationKey))
                {
                    customerRelationship = await databaseService.Customers
                            .FindAsync([request.CustomerRelationKey], cancellationToken);
                    if (customerRelationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelation, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var customerRelation = await databaseService.CustomerRelationShips
                        .Include(a => a.Relationship)
                        .FirstOrDefaultAsync(a => a.CustomerId.Equals(request.CustomerRelationKey) && a.CustomerIdRefer.Equals(request.CustomerKey), cancellationToken);

                    if (customerRelation is null || customerRelation.Relationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelationship, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    customerRelationshipName = customerRelation.Relationship.Name;
                    customerRelationshipId = customerRelation.Relationship.Id;
                }

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customer.CareerId != request.CareerId)
                {
                    customer.CareerId = request.CareerId ?? string.Empty;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                // Cập nhật thông tin bảo hiểm y tế
                if (!string.IsNullOrEmpty(request.Insurance?.InsuranceNo)
                    && (customer.HealthInsuranceNo != request.Insurance?.InsuranceNo
                    || customer.HealthInsurancePlaceId != request.Insurance?.RegisterPlaceID))
                {
                    customer.HealthInsuranceNo = request.Insurance?.InsuranceNo;
                    customer.HealthInsurancePlaceId = request.Insurance?.RegisterPlaceID;
                    if (!string.IsNullOrEmpty(request.Insurance?.RegisterPlaceName))
                    {
                        customer.HealthInsurancePlace = request.Insurance?.RegisterPlaceName;
                    }
                    else
                    {
                        var res = await mediator.Send(new GetTransferReferralHospitals(), cancellationToken);
                        customer.HealthInsurancePlace = res.Success && res.Data != null
                            ? res.Data.FirstOrDefault(h => h.Id == request.Insurance?.RegisterPlaceID)?.Name ?? string.Empty
                            : string.Empty;
                    }
                    customer.HealthInsuranceFromDate = request.Insurance?.FromDate;
                    customer.HealthInsuranceExpiredDate = request.Insurance?.ExpiredDate;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.EducationLevel)
                    && customer.EducationLevel != request.EducationLevel)
                {
                    customer.EducationLevel = request.EducationLevel;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkPlace)
                    && customer.WorkPlace != request.WorkPlace)
                {
                    customer.WorkPlace = request.WorkPlace;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkAddress)
                    && customer.WorkAddress != request.WorkAddress)
                {
                    customer.WorkAddress = request.WorkAddress;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if ((!string.IsNullOrEmpty(request.ProvinceId) && customer.ProvinceId != request.ProvinceId) ||
                    (!string.IsNullOrEmpty(request.WardId) && customer.WardId != request.WardId) ||
                    (!string.IsNullOrEmpty(request.DistrictId) && customer.DistrictId != request.DistrictId) ||
                    (!string.IsNullOrEmpty(request.Street) && customer.Street != request.Street) ||
                    (!customer.IsSyncedTwoLevelAddress && currentHospitalService.CurrentHospital.IsTwoLevelAddress))
                {
                    var isRequestTwoLevelAddress = request.IsTwoLevelAddress.GetValueOrDefault();

                    var ward = await wardRepository.GetWardByIdAsync(request.WardId!, isRequestTwoLevelAddress, cancellationToken);

                    string? districtName = null;
                    if (!isRequestTwoLevelAddress)
                    {
                        districtName = (await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId, cancellationToken)).FirstOrDefault(p => p.Id == request.DistrictId)?.Name;
                    }

                    var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);
                    var province = provinces.FirstOrDefault(p => (isRequestTwoLevelAddress ? p.NewId : p.Id) == request.ProvinceId);

                    if (ward != null && province != null && (isRequestTwoLevelAddress || districtName != null))
                    {
                        customer.IsSyncedTwoLevelAddress = isRequestTwoLevelAddress;

                        if (isRequestTwoLevelAddress)
                        {
                            customer.ProvinceId = province?.NewId;
                            customer.DistrictId = string.Empty;
                            customer.WardId = ward?.NewId;
                            customer.Address = $"{customer.Street}, {ward?.NewName}, {province?.NewName}";
                        }
                        else
                        {
                            customer.ProvinceId = request.ProvinceId;
                            customer.DistrictId = request.DistrictId;
                            customer.WardId = request.WardId;
                            customer.Address = $"{customer.Street}, {ward?.Name}, {districtName}, {province?.Name}";
                        }

                        customer.Street = request.Street;

                        customer.SetUpdate(currentUserService.UserName);
                        databaseService.Customers.Update(customer);
                    }
                }

                var customerHospital = await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.CustomerId == customer.Id && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken);

                var isNotFoundCustomerHospital = customerHospital == null;
                if (isNotFoundCustomerHospital)
                {
                    customerHospital = new CustomerHospital { HospitalId = currentHospitalService.CurrentHospital.HospitalId, CustomerId = customer.Id };
                    customerHospital.SetCreate(currentUserService.UserName);
                }
                else
                {
                    customerHospital!.SetUpdate(currentUserService.UserName);
                }

                customerHospital.IsFavourite = true;

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customerHospital.CareerId != request.SocialCareerId)
                {
                    customerHospital.CareerId = request.SocialCareerId ?? string.Empty;
                }

                bool getResult = false;
                string message = string.Empty;

                // 0. Nếu khách hàng chưa có mã bệnh nhân thì gọi sang HIS tích hợp mã bệnh nhân
                if (string.IsNullOrEmpty(customerHospital.PatientCode))
                {
                    if (!string.IsNullOrEmpty(request.PatientId) && !string.IsNullOrEmpty(request.PatientCode))
                    {
                        customerHospital.PatientId = request.PatientId;
                        customerHospital.PatientCode = request.PatientCode;
                    }
                    else
                    {
                        (getResult, message, errorType, HisCustomerDto hisCustomerDto) = await hisService.GetCustomerHis(customer);
                        Log.Information("{LogPrefix} GetPationCode from HIS: {getResult} - - - message: {message} - - - patientCode: {patientCode} ", currentHospitalService.LogPrefix, getResult, message, hisCustomerDto.PatientCode);
                        if (getResult && !string.IsNullOrEmpty(hisCustomerDto.PatientCode))
                        {
                            //1. Save patient code
                            customerHospital.PatientId = hisCustomerDto.PatientId;
                            customerHospital.PatientCode = hisCustomerDto.PatientCode;
                        }
                    }
                }

                // 1. Tạo và lưu phiếu tiếp nhận
                List<HealthService>? healthServices = [];
                HealthService? healthService = null;
                Register register = new();
                Clinic? clinic = null;
                Clinic? subClinic = null;
                ExameType? exameType = null;

                //luồng mua gói thành viên
                if (request.RegisterType == "DANG_KY_THANH_VIEN")
                {
                    //get package services
                    var getPackageService = await databaseService.PackageServices
                        .FindAsync([request.HealthServiceId], cancellationToken);

                    if (getPackageService is null)
                    {
                        result.Set(false, "Không tìm thấy dịch vụ khám", errorType);
                        Log.Warning("{LogPrefix} Không tìm thấy dịch vụ khám '{HealthServiceId}'", currentHospitalService.LogPrefix, request.HealthServiceId);
                        return result;
                    }

                    healthService = new HealthService
                    {
                        Id = getPackageService.Id,
                        Name = getPackageService.Name,
                        Code = getPackageService.Id,
                        UnitPrice = getPackageService.PromotionPrice,
                        ExameTypeId = request.ExameTypeId,
                        ClinicId = request.ClinicId,
                        ClinicCode = request.ClinicCode,
                        SubClinicId = request.SubClinicId,
                        ClinicGroupId = request.ClinicGroupId,
                        ClinicGroupCode = request.ClinicGroupCode,
                        InsurancePrice = getPackageService.PromotionPrice
                    };
                }
                else
                {
                    //nếu his không có danh sách dịch vụ (Hòa Bình luồng bảo hiểm) => lấy mặc định
                    if ((request.IsInsurance && currentHospitalService.CurrentHospital.IsSkipGetInsuranceServices)
                        || (!request.IsInsurance && currentHospitalService.CurrentHospital.IsSkipGetServices))
                    {
                        var getService = await mediator.Send(new GetDefaultHealthServices
                        {
                            ClinicId = request.ClinicId,
                            ExameTypeId = request.ExameTypeId,
                            IsInsurance = request.IsInsurance,
                            SubClinicId = request.SubClinicId ?? string.Empty,
                            ClinicCode = request.ClinicCode ?? string.Empty,
                            IsGetAll = true
                        }, cancellationToken);

                        if (getService.Data == null)
                        {
                            Log.Error("{LogPrefix} GetDefaultHealthServices: Default Health Services is null", currentHospitalService.LogPrefix);
                            result.Set(false, "Không thể lấy danh sách dịch vụ khám", errorType);
                            return result;
                        }
                        getResult = getService.Success;
                        message = getService.Messages;
                        healthServices = getService.Data;
                    }
                    else //lấy từ HIS
                    {
                        //get health services mediator
                        var getHealthServices = await mediator.Send(new GetHealthServicesOfClinic
                        {
                            ExameTypeId = request.ExameTypeId,
                            ClinicId = request.ClinicId ?? string.Empty,
                            SubClinicId = request.SubClinicId ?? string.Empty,
                            ClinicCode = request.ClinicCode,
                            HealthInsuranceNo = request.Insurance?.InsuranceNo,
                            IsIgnoreFilter = true
                        }, cancellationToken);
                        getResult = getHealthServices.Success;
                        message = getHealthServices.Messages;
                        healthServices = getHealthServices.Data;
                    }

                    Log.Information("{LogPrefix} Get healthServices getResult: {getResult} - - - message: {message} - - - HealthServices: {@HealthServices} ", currentHospitalService.LogPrefix, getResult, message, healthServices);
                    if (healthServices == null || healthServices.Count == 0)
                    {
                        result.Set(false, "Không thể lấy danh sách dịch vụ khám", errorType);
                        Log.Warning("{LogPrefix} Không thể lấy danh sách dịch vụ khám", currentHospitalService.LogPrefix);
                        return result;
                    }

                    healthService = healthServices.FirstOrDefault(x => x.Id == request.HealthServiceId
                        && (string.IsNullOrEmpty(request.ExameTypeId) || x.ExameTypeId == request.ExameTypeId)
                        && (string.IsNullOrEmpty(request.ClinicGroupId) || x.ClinicGroupId == request.ClinicGroupId)
                        && (string.IsNullOrEmpty(request.ClinicGroupCode) || x.ClinicGroupCode == request.ClinicGroupCode));
                    if (healthService is null)
                    {
                        result.Set(false, $"Không tìm thấy dịch vụ '{request.HealthServiceId}'", errorType);
                        Log.Warning("{LogPrefix} Không tìm thấy dịch vụ '{HealthServiceId}' - Danh sách: {@HealthServices}", currentHospitalService.LogPrefix, request.HealthServiceId, healthServices);
                        return result;
                    }

                    if (string.IsNullOrEmpty(healthService.ClinicId))
                        healthService!.ClinicId = request.MaPhong;

                    if (string.IsNullOrWhiteSpace(request.ClinicId))
                    {
                        result.Set(false, "Nhóm chuyên khoa không được để trống", ErrorTypeEnum.MediPayError);
                        Log.Warning("{LogPrefix} Nhóm chuyên khoa không được để trống", currentHospitalService.LogPrefix);
                        return result;
                    }

                    // get clinic
                    (getResult, message, errorType, List<Clinic>? clinics) = await hisService.GetClinics(request.ExameTypeId, currentHospitalService.KioskId);

                    clinic = clinics?.Find(x => x.Id == healthService?.ClinicId && x.Code == healthService.ClinicCode);
                    subClinic = new Clinic();
                    if (clinic is null)
                    {
                        Log.Warning("{LogPrefix} Không tìm thấy nhóm chuyên khoa '{ClinicId}' - '{ClinicCode}' - Danh sách: {@Clinics}", currentHospitalService.LogPrefix, healthService?.ClinicId, healthService?.ClinicCode, clinics);
                    }
                    else if (!string.IsNullOrEmpty(request.SubClinicId))// check sub clinic if exist
                    {
                        subClinic = clinic.Children?.Find(x => x.Id == request.SubClinicId);

                        if (subClinic is null)
                        {
                            result.Set(false, $"Không tìm thấy chuyên khoa '{request.SubClinicId}'", ErrorTypeEnum.MediPayError);
                            Log.Warning("{LogPrefix} Không tìm thấy chuyên khoa '{SubClinicId}' - Danh sách: {@Clinics}", currentHospitalService.LogPrefix, request.SubClinicId, clinics);
                            return result;
                        }
                    }

                    // get exame type
                    var exameTypes = await mediator.Send(new GetExameTypes(), cancellationToken);
                    if (!exameTypes.Success)
                    {
                        result.Set(false, $"Không tìm thấy danh sách loại khám", errorType);
                        Log.Warning("{LogPrefix} Không tìm thấy loại khám '{ExameTypeId}' - Danh sách: {@ExameTypes}", currentHospitalService.LogPrefix, request.ExameTypeId, exameTypes.Data);
                        return result;
                    }

                    exameType = exameTypes.Data?.FirstOrDefault(x => x.Id == request.ExameTypeId
                        && (!string.IsNullOrEmpty(x.Id) || x.IsInsurance == request.IsInsurance));
                    if (exameType is null)
                    {
                        //     result.Set(false, $"Không tìm thấy loại khám '{request.ExameTypeId}'", errorType);
                        Log.Warning("{LogPrefix} Không tìm thấy loại khám '{ExameTypeId}' - Danh sách: {@ExameTypes}", currentHospitalService.LogPrefix, request.ExameTypeId, exameTypes.Data);
                        //     return result;
                    }
                }

                //Luồng tạm ứng
                healthService!.UnitPrice ??= request.AdvancePayment;

                decimal unitPrice = healthService.UnitPrice ?? 0;
                if (request.IsInsurance) // Nếu là bảo hiểm
                {
                    // Nếu bệnh viện không tính tiền bảo hiểm
                    if (currentHospitalService.CurrentHospital.IsIgnoreInsurancePayment)
                    {
                        unitPrice = 0;
                    }
                    // Nếu bệnh viện tính tiền bảo hiểm dựa trên giá thu thêm
                    else if (healthService.ExtraPrice.HasValue && healthService.ExtraPrice > 0)
                    {
                        unitPrice = healthService.ExtraPrice ?? 0;
                    }
                    else if (healthService.InsurancePrice.HasValue)
                    {
                        // Nếu không có giá thì lấy giá mặc định
                        unitPrice = healthService.InsurancePrice ?? 0;
                    }
                }

                register.CustomerId = customer.Id;
                register.CustomerRelationId = customerRelationship.Id;
                register.Number = IdentityHelper.Guid(15);
                register.MembershipId = request.MembershipId;
                register.HealthServiceId = healthService.Id;
                var registerAt = DateTimeHelper.GetCurrentLocalDateTime();
                register.RegisterAt = registerAt.AddHours(-7);
                register.SetCreate(currentUserService.UserName);
                register.SubTotalAmount = unitPrice;
                // Tính toán discount ở đây
                register.TotalAmount = unitPrice - register.DiscountAmount;
                register.HealthInsurance = request.IsInsurance ? 1 : 0; //. Không dùng thẻ BHYT
                register.ClinicId = healthService.ClinicId;
                register.ClinicCode = healthService?.ClinicCode ?? string.Empty;
                register.Clinic = clinic?.Name ?? request.ClinicName ?? string.Empty;
                register.ClinicGroupId = healthService?.ClinicGroupId ?? string.Empty;
                register.ClinicGroupCode = healthService?.ClinicGroupCode ?? string.Empty;
                register.ClinicGroup = healthService?.ClinicGroup ?? string.Empty;
                register.SubClinicId = request.SubClinicId ?? string.Empty;
                register.SubClinic = subClinic?.Name ?? string.Empty;
                register.HospitalId = currentHospitalService.CurrentHospital.HospitalId;
                register.DeviceId = currentHospitalService.KioskId;
                register.ExameTypeId = request.ExameTypeId;
                register.ExameType = exameType?.Name ?? string.Empty;
                register.HealthcareServiceTierId = request.HealthcareServiceTierId;
                register.HealthcareServiceTypeId = request.HealthcareServiceTypeId;
                register.MedicalTreatmentCategoryId = request.MedicalTreatmentCategoryId;
                register.MedicalTreatmentCategoryHisId = request.MedicalTreatmentCategoryHisId;
                register.ReasonForVisit = request.ReasonForVisit ?? string.Empty;
                register.TransferReferralDocumentNumber = request.TransferReferralDocumentNumber ?? string.Empty;
                register.TransferReferralDiseaseCode = request.TransferReferralDiseaseCode ?? string.Empty;
                register.TransferReferralUnit = request.TransferReferralUnit ?? string.Empty;
                register.ReferralLevel = request.Insurance?.ReferralLevel ?? string.Empty;
                register.PaymentType = request.PaymentType ?? string.Empty;
                (bool createResult, string createMessage, errorType, string registerNo, RegisterFormResponseDto resRegisterFrom)
                    = await hisService.CreateRegisterForm(new RegisterFormRequestDto
                    {
                        Customer = customer,
                        CustomerRelationship = customerRelationship,
                        CustomerHospital = customerHospital,
                        Service = healthService!,
                        IsInsurance = request.IsInsurance,
                        Insurance = request.Insurance,
                        HealthcareServiceTierId = request.HealthcareServiceTierId,
                        HealthcareServiceTypeId = request.HealthcareServiceTypeId,
                        MedicalTreatmentCategoryId = request.MedicalTreatmentCategoryId,
                        MedicalTreatmentCategoryHisId = request.MedicalTreatmentCategoryHisId,
                        ReasonForVisit = request.ReasonForVisit,
                        TransferReferralDocumentNumber = request.TransferReferralDocumentNumber,
                        TransferReferralDiseaseCode = request.TransferReferralDiseaseCode,
                        TransferReferralDiseaseCodeAndName = request.TransferReferralDiseaseCodeAndName,
                        TransferReferralUnit = request.TransferReferralUnit,
                        TransferReferralType = request.TransferReferralType,
                        TransferReferralReason = request.TransferReferralReason,
                        TransferReferralDate = request.TransferReferralDate,
                        TransferReferralDiagnosisInfo = request.TransferReferralDiagnosisInfo,
                        CustomerRelationshipName = customerRelationshipName,
                        CustomerRelationshipId = customerRelationshipId,
                        DeviceId = currentHospitalService.KioskId,
                        PaymentAmount = unitPrice,
                        BloodPressure = request.BloodPressure,
                        HeartRate = request.HeartRate,
                        RespiratoryRate = request.RespiratoryRate,
                        BloodOxygen = request.BloodOxygen,
                        Height = request.Height,
                        Weight = request.Weight,
                        PulseRate = request.PulseRate,
                        Temperature = request.Temperature,
                        PaymentType = request.PaymentType,
                        IsOnARVTreatment = request.IsOnARVTreatment,
                        Priority = request.UuTien,
                        RegisterTime = registerAt,
                        DiagnosisBeforeAdmission = request.DiagnosisBeforeAdmission,
                        DiagnosisBeforeAdmissionICD = request.DiagnosisBeforeAdmissionICD,
                        AccidentCode = request.AccidentCode,
                        BloodType = request.BloodType,
                    });

                register.IsHisCreateFormSuccess = createResult;
                register.HisMessageWhenCreateForm = createMessage;

                Log.Information("{LogPrefix} Get Health Services getResult: {createResult} - - - message: {createResult} - - - patientCode: {registerNo} ", currentHospitalService.LogPrefix, createResult, createResult, registerNo);
                if (!createResult)
                {
                    result.Set(false, createMessage, ErrorTypeEnum.HisError);
                    return result;
                }

                //lưu cache rotate (Tai mũi họng)
                if (currentHospitalService.CurrentHospital.HisVersion == "v12" && createResult)
                {
                    _ = cachedService.SetAsync("HealthServices_SortIndex" + currentHospitalService.CurrentHospital.HospitalId + request.ExameTypeId + request.ClinicId + request.SubClinicId + request.ClinicCode, new SortIndexCache
                    {
                        ClinicId = healthService!.Id,
                        HealthServiceCode = healthService.Code,
                        HealthServiceId = healthService.Id,
                    }, expireSecond: 43200);
                }

                /// Luồng bệnh viện Hoà Bình cho có tạo phiếu đăng ký
                /// Nên tạo thành công. => tạo phiếu đăng ký + thanh toán ở GT luôn
                if (resRegisterFrom != null)
                {
                    // Nếu giá đã xác định khi lấy dịch vụ, thì giá trị này sẽ bằng null
                    // Nếu giá chỉ được xác định khi tạo phiếu, thì lấy giá này
                    if (resRegisterFrom.ResponseUnitPrice.HasValue)
                    {
                        unitPrice = resRegisterFrom.ResponseUnitPrice.Value;
                        register.SubTotalAmount = unitPrice;
                        register.TotalAmount = unitPrice - register.DiscountAmount;
                    }

                    register.QueueNumber = resRegisterFrom.QueueNumber;
                    register.QueueNumberPriority = resRegisterFrom.QueueNumberPriority == "1";
                    register.RefDocNo = resRegisterFrom.RefDocNo;
                    register.ExaminationLocation = resRegisterFrom.ExaminationLocation;
                    register.MedicalTreatmentCategoryName = resRegisterFrom.MedicalTreatmentCategoryName;
                    register.RateOfInsurance = resRegisterFrom.RateOfInsurance;
                    register.PatientCode = resRegisterFrom.PatientCode;
                    register.LinkCode = resRegisterFrom.LinkCode;
                    register.ExpectedAppointmentAt = resRegisterFrom.ExpectedAppointmentAt;

                    if (!string.IsNullOrEmpty(resRegisterFrom.HealthcareServiceTierId))
                    {
                        register.HealthcareServiceTierId = resRegisterFrom.HealthcareServiceTierId;
                    }

                    var receipt = new Receipt
                    {
                        Number = IdentityHelper.Guid(15),
                        CustomerId = customer.Id,
                        TotalAmount = unitPrice,
                        ReceiptDate = DateTime.UtcNow,
                        RegisterNumber = register.Number,
                        Status = unitPrice > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                        DeviceId = currentHospitalService.KioskId,
                        HospitalId = register.HospitalId,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.ReceiptRefNo,
                        CreatedAt = DateTime.UtcNow
                    };

                    var payment = new Payment
                    {
                        Id = receipt.Number,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.PaymentRefNo,
                        PaymentDate = DateTime.UtcNow,
                        PaymentAmount = receipt.TotalAmount,
                        ReceiptNumber = receipt.Number,
                        CreatedAt = DateTime.UtcNow,
                        Status = unitPrice > 0 ? PaymentConstant.WaitForPayment : PaymentConstant.Success,
                        InvoiceInfoRef = resRegisterFrom.RefDocNo,
                        IsHisGenQr = currentHospitalService.CurrentHospital.IsGenQR,
                        HospitalId = register.HospitalId
                    };

                    //tạo QR code khi tạo phiếu đăng ký nếu cấu hình bệnh viện cho phép
                    if (currentHospitalService.CurrentHospital.IsGenQRWhenCreateRegister && unitPrice > 0)
                    {
                        //3. Call API Gen Qr
                        var config = new PaymentConfigModel()
                        {
                            Url = paymentConfig.PaymentUrl,
                            SecretKey = paymentConfig.SecretKey
                        };

                        //get hospital
                        var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
                        if (hospital == null)
                        {
                            result.Set(false, "Không tìm thấy bệnh viện", ErrorTypeEnum.MediPayError);
                            return result;
                        }

                        string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;

                        //remove special characters include space, 0-9a-zA-Z, and -, .
                        paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
                        // limit to 45 characters
                        paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
                        var paymentRequest = new CreateQrRequest()
                        {
                            MerchantId = hospital.MerchantId,
                            InvoiceId = payment.Id,
                            Type = PaymentConstant.DefaultType,
                            TransactionAmount = (double)payment.PaymentAmount,
                            Ipn = paymentConfig.IpnUrl,
                            TransactionDescription = $"THANH TOAN PHIEU THU {paymentDescription}",
                        };

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

                        (bool processResult, string processMessage, CreateQrResponse? res) = await
                            PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                            currentHospitalService.LogPrefix, processResult, processMessage, res);

                        config.Dispose();

                        if (processResult)
                        {
                            payment.QrCode = res!.QrCode;
                            payment.RefNo = res!.PaymentCode;
                            receipt.QrCode = payment.QrCode;
                            payment.RefDescReq = res!.TransactionDescription;
                        }
                    }

                    databaseService.Receipts.Add(receipt);
                    databaseService.Payments.Add(payment);

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientCode)
                        && resRegisterFrom.PatientCode != customerHospital.PatientCode)
                    {
                        customerHospital.PatientCode = resRegisterFrom.PatientCode;
                    }

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientId)
                        && resRegisterFrom.PatientId != customerHospital.PatientId)
                    {
                        customerHospital.PatientId = resRegisterFrom.PatientId;
                    }
                }

                // Save to database
                if (isNotFoundCustomerHospital)
                {
                    databaseService.CustomerHospitals.Add(customerHospital);
                }
                else
                {
                    databaseService.CustomerHospitals.Update(customerHospital);
                }

                register.ServiceName = healthService?.Name ?? string.Empty;
                register.RefNo = registerNo;
                register.RefRegisterNumber = request.RefRegisterNumber;
                register.RegisterType = string.IsNullOrEmpty(request.RegisterType) ? register.RegisterType : request.RegisterType;

                if (!string.IsNullOrEmpty(request.PatientBookingNumber))
                {
                    var booking = await databaseService.PatientBookings
                            .FindAsync([request.PatientBookingNumber], cancellationToken);
                    if (booking is null)
                    {
                        Log.Warning("{LogPrefix} Không tìm thấy booking '{PatientBookingNumber}'", currentHospitalService.LogPrefix, request.PatientBookingNumber);
                        result.Set(false, "Không tìm thấy booking", ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    register.PatientBookingNumber = booking.Number;
                    booking.Status = PatientBookingStatus.SUCCESS;
                    databaseService.PatientBookings.Update(booking);
                }
                databaseService.Registers.Add(register);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);
                Log.Information("{LogPrefix} saveResult: {saveResult} ", currentHospitalService.LogPrefix, saveResult);
                if (saveResult > 0)
                {
                    errorType = ErrorTypeEnum.NoError;
                    result.Set(true, RegisterConstant.SaveChangesSuccess, new CreateRegisterFormResponseDto
                    {
                        Number = register.Number,
                        RefNo = register.RefNo,
                        HealthInsurance = register.HealthInsurance,
                        UnitPrice = register.TotalAmount
                    });
                }
                else
                {
                    result.Set(false, RegisterConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Có lỗi xảy ra khi tạo phiếu tiếp nhận", currentHospitalService.LogPrefix);
                result.Set(false, RegisterConstant.CreateError, errorType);
            }

            return result;
        }
    }
}