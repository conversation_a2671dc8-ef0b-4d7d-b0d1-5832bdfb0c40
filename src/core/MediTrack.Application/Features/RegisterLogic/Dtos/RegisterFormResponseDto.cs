namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormResponseDto
    {
        public string RegisterNumber { get; set; } = string.Empty;
        public string? PaymentRefNo { get; set; }
        public string? ReceiptRefNo { get; set; }
        public string Clinic { get; set; } = string.Empty;
        public string QueueNumber { get; set; } = string.Empty;
        public string QueueNumberPriority { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string RateOfInsurance { get; set; } = string.Empty;
        public string PatientId { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string LinkCode { get; set; } = string.Empty;
        public string MedicalTreatmentCategoryName { get; set; } = string.Empty;
        /// <summary>
        /// Nếu giá đã xác định khi lấy dịch vụ, thì giá trị này sẽ bằng null
        /// Nếu giá chỉ được xác định khi tạo phiếu, thì lấy giá này
        /// </summary>
        public decimal? ResponseUnitPrice { get; set; }
        public DateTime? ExpectedAppointmentAt { get; set; }
        public string? HealthcareServiceTierId { get; set; }
        
    }
}
