using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class PatientBookingDto
    {
        public string CustomerId { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerAddress { get; set; } = string.Empty;
        public string WorkAddress { get; set; } = string.Empty;
        public string? EducationLevel { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public bool IsInsurance { get; set; }
        public bool IsOnARVTreatment { get; set; }
        public string? ReasonForVisit { get; set; }
        public int PriorityId { get; set; }
        public string PriorityName { get; set; } = string.Empty;
        public string ExameTypeId { get; set; } = string.Empty;
        public string ExameType { get; set; } = string.Empty;
        public string ClinicId { get; set; } = string.Empty;
        public string Clinic { get; set; } = string.Empty;
        public string SubClinicId { get; set; } = string.Empty;
        public string SubClinic { get; set; } = string.Empty;
        public string ClinicCode { get; set; } = string.Empty;
        public string ClinicGroupId { get; set; } = string.Empty;
        public string ClinicGroup { get; set; } = string.Empty;
        public string ClinicGroupCode { get; set; } = string.Empty;
        public string HealthServiceId { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public string? HealthcareServiceTypeId { get; set; }
        public string? MedicalTreatmentCategoryId { get; set; }
        public string? PaymentNumber { get; set; }
        public bool PaymentStatus { get; set; }
        public string? PaymentType { get; set; }
        public decimal? PaymentAmount { get; set; }
        public string? PaymentAmountDisplay { get; set; }
        public string? Status { get; set; }
    }
}