﻿using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterDocumentDto
    {
        public string HospitalName { get; set; } = string.Empty;
        public string Logo { get; set;} = string.Empty;
        public string RegisterNo { get; set;} = string.Empty;
        public string InsuranceNumber { get; set; } = string.Empty;
        public string PrimaryHospital { get; set; } = string.Empty;
        public string RegisterDate { get; set; } = string.Empty;
        public string RegisterNumber { get; set;} = string.Empty;
        public string ClinicId { get; set; } = string.Empty;
        public string ClinicName { get; set; } = string.Empty;
        public string HealthServiceId { get; set; } = string.Empty;
        public string HealthServiceName { get; set; } = string.Empty;
        public string UnitPrice { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string LinkCode { get; set; } = string.Empty;
        public string RateOfInsurance { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public string PaymentStatusDesc
        {
            get => this.PaymentStatus switch
                {
                    PaymentConstant.FailPayment => "Thất bại",
                    PaymentConstant.Success => "Thành công",
                    PaymentConstant.Partial  => "Thanh toán một phần",
                    _ => "Chưa thanh toán"
                };
        }

        public string CustomerName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; } = string.Empty;
        public string CompanyName { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string TaxNumber { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public string HospitalCode { get; set; } = string.Empty;
        public string HospitalTaxCode { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;

        public DateTime? CreateAt { get; set; }
        public string Clinic { get; set; } = string.Empty;
        public int Priority { get; set; } = 0;
    }
}
