﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class HistoryRegisterDto
    {
        public string? Number { get; set; }
        public DateTime RegisterTime { get; set; }
        public string? Clinic { get; set; }
        public string? ExameType { get; set; }
        public string? ServiceName { get; set; }
        public int HealthInsurance { get; set; }
        public bool IsDisabled { get; set; }
    }
}
