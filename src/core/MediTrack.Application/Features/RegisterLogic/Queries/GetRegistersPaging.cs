﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetRegistersPaging : IRequest<BaseCommandResultWithData<BasePaging<Register>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? HealthServiceIds { get; set; } = [];
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
    }

    public class GetRegistersPagingHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService)
        : IRequestHandler<GetRegistersPaging, BaseCommandResultWithData<BasePaging<Register>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<BasePaging<Register>>> Handle(
            GetRegistersPaging request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<BasePaging<Register>>();

            var query = databaseService.Registers
                .Where(x => x.RegisterAt >= request.FromDate &&
                            x.RegisterAt <= request.ToDate &&
                            (request.HealthServiceIds == null || request.HealthServiceIds.Count == 0 || (x.HealthServiceId != null
                            && request.HealthServiceIds.Contains(x.HealthServiceId))));

            var count = await query.CountAsync();
            BasePaging<Register> data = new BasePaging<Register>
            {
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                TotalItem = count,
                TotalPage = (count + request.PageSize - 1) / request.PageSize,
                Items = await query.Skip(request.PageSize * request.PageIndex).Take(request.PageSize)
                    .ToListAsync(),
            };

            result.Set(true, RegisterConstant.Ok, data);

            return result;
        }
    }
}
