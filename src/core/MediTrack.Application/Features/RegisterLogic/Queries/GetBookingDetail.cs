using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ClinicLogic.Queries;
using MediTrack.Application.Features.ExameTypeLogic.Queries;
using MediTrack.Application.Features.HealthServiceLogic.Queries;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetBookingDetail : IRequest<BaseCommandResultWithData<PatientBookingDto>>
    {
        public string? Number { get; set; }
        public bool IsBooking { get; set; }
    }

    public class GetBookingDetailHandler(IDatabaseService databaseService,
    IMediator mediator,
    ICurrentHospitalService currentHospitalService) : IRequestHandler<GetBookingDetail, BaseCommandResultWithData<PatientBookingDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<PatientBookingDto>> Handle(GetBookingDetail request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<PatientBookingDto>();
            if (string.IsNullOrEmpty(request.Number))
            {
                result.Set(false, "Số phiếu không được để trống", ErrorTypeEnum.MediPayError);
                return result;
            }
            PatientBookingDto data = new();
            if (request.IsBooking)
            {
                var booking = await databaseService.PatientBookings
                    .FindAsync([request.Number], cancellationToken);
                if (booking == null)
                {
                    result.Set(false, "Không tìm thấy thông tin đăng ký khám", ErrorTypeEnum.MediPayError);
                    return result;
                }
                data = booking.Adapt<PatientBookingDto>();
                data.PriorityId = booking.UuTien;
                data.PriorityName = booking.UuTien switch
                {
                    0 => PriorityConstant.Normal,
                    1 => PriorityConstant.Priority,
                    _ => "-"
                };
                var customer = await databaseService.Customers
                    .FindAsync(booking.CustomerId, cancellationToken);
                if (customer != null)
                {
                    data.CustomerId = customer.Id;
                    data.CustomerName = customer.GetFullName();
                    data.CustomerAddress = customer.Address ?? string.Empty;
                }
                var payment = await databaseService.Payments
                                .Include(x => x.Receipt)
                                .ThenInclude(y => y!.Register)
                                .FirstOrDefaultAsync(x => x.Receipt != null &&
                                                        x.Receipt.Register != null &&
                                                        x.Receipt.Register.PatientBookingNumber == request.Number,
                                                        cancellationToken);
                data.PaymentNumber = payment?.Id ?? string.Empty;
                data.PaymentStatus = payment?.Status == "PAID";

                // get exame type
                var exameTypes = await mediator.Send(new GetExameTypes(), cancellationToken);
                if (!exameTypes.Success)
                {
                    result.Set(false, $"Không tìm thấy danh sách loại khám", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không tìm thấy loại khám '{ExameTypeId}' - Danh sách: {@ExameTypes}", currentHospitalService.LogPrefix, data.ExameTypeId, exameTypes.Data);
                    return result;
                }

                var exameType = exameTypes.Data?.FirstOrDefault(x => x.Id == data.ExameTypeId);
                data.ExameType = exameType?.Name ?? string.Empty;

                // get clinic
                (bool getResult, string message, ErrorTypeEnum errorType, List<Clinic>? clinics) = await hisService.GetClinics(data.ExameTypeId, currentHospitalService.KioskId);

                var clinic = clinics?.Find(x => x.Id == data.ClinicId && x.Code == data.ClinicCode);

                var subClinic = new Clinic();
                if (clinic is null)
                {
                    Log.Warning("{LogPrefix} Không tìm thấy nhóm chuyên khoa '{ClinicId}' - '{ClinicCode}' - Danh sách: {@Clinics}", currentHospitalService.LogPrefix, data.ClinicId, data.ClinicCode, clinics);
                }
                else if (!string.IsNullOrEmpty(data.SubClinicId)) // check sub clinic if exist
                {
                    subClinic = clinic.Children?.Find(x => x.Id == data.SubClinicId);

                    if (subClinic is null)
                    {
                        result.Set(false, $"Không tìm thấy chuyên khoa '{data.SubClinicId}'", ErrorTypeEnum.MediPayError);
                        Log.Warning("{LogPrefix} Không tìm thấy chuyên khoa '{SubClinicId}' - Danh sách: {@Clinics}", currentHospitalService.LogPrefix, data.SubClinicId, clinics);
                        return result;
                    }
                }
                data.Clinic = clinic?.Name ?? string.Empty;
                data.SubClinic = subClinic?.Name ?? string.Empty;

                // get health service
                List<HealthService>? healthServices = [];
                HealthService? healthService = null;
                if ((data.IsInsurance && currentHospitalService.CurrentHospital.IsSkipGetInsuranceServices)
                    || (!data.IsInsurance && currentHospitalService.CurrentHospital.IsSkipGetServices))
                {
                    var getService = await mediator.Send(new GetDefaultHealthServices
                    {
                        ClinicId = data.ClinicId,
                        ExameTypeId = data.ExameTypeId,
                        IsInsurance = data.IsInsurance,
                        SubClinicId = data.SubClinicId ?? string.Empty,
                        ClinicCode = data.ClinicCode ?? string.Empty,
                        IsGetAll = true
                    }, cancellationToken);

                    if (getService.Data == null)
                    {
                        Log.Error("{LogPrefix} GetDefaultHealthServices: Default Health Services is null", currentHospitalService.LogPrefix);
                        result.Set(false, "Không thể lấy danh sách dịch vụ khám", ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    getResult = getService.Success;
                    message = getService.Messages;
                    healthServices = getService.Data;
                }
                else // lấy từ HIS
                {
                    // get health services mediator
                    var getHealthServices = await mediator.Send(new GetHealthServicesOfClinic
                    {
                        ExameTypeId = data.ExameTypeId,
                        ClinicId = data.ClinicId ?? string.Empty,
                        SubClinicId = data.SubClinicId ?? string.Empty,
                        ClinicCode = data.ClinicCode,
                        HealthInsuranceNo = null,
                        IsIgnoreFilter = customer == null,
                        DateOfBirth = customer?.DateOfBirth?.ToString("dd/MM/yyyy") ?? string.Empty,
                        Gender = customer?.Sex
                    }, cancellationToken);
                    getResult = getHealthServices.Success;
                    message = getHealthServices.Messages;
                    healthServices = getHealthServices.Data;
                }

                Log.Information("{LogPrefix} Get healthServices getResult: {getResult} - - - message: {message} - - - HealthServices: {@HealthServices} ", currentHospitalService.LogPrefix, getResult, message, healthServices);
                if (healthServices == null || healthServices.Count == 0)
                {
                    result.Set(false, "Không thể lấy danh sách dịch vụ khám", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không thể lấy danh sách dịch vụ khám");
                    return result;
                }

                healthService = healthServices.FirstOrDefault(x => x.Id == data.HealthServiceId
                    && (string.IsNullOrEmpty(data.ExameTypeId) || x.ExameTypeId == data.ExameTypeId)
                    && (string.IsNullOrEmpty(data.ClinicGroupId) || x.ClinicGroupId == data.ClinicGroupId)
                    && (string.IsNullOrEmpty(data.ClinicGroupCode) || x.ClinicGroupCode == data.ClinicGroupCode));
                if (healthService is null)
                {
                    result.Set(false, $"Không tìm thấy dịch vụ '{data.HealthServiceId}'", ErrorTypeEnum.MediPayError);
                    Log.Warning("{LogPrefix} Không tìm thấy dịch vụ '{HealthServiceId}' - Danh sách: {@HealthServices}", currentHospitalService.LogPrefix, data.HealthServiceId, healthServices);
                    return result;
                }
                data.ClinicGroupId = healthService.ClinicGroup ?? string.Empty;
                data.ServiceName = healthService.Name ?? string.Empty;

                // Luồng tạm ứng
                healthService!.UnitPrice ??= booking.AdvancePayment;

                decimal unitPrice = healthService.UnitPrice ?? 0;
                if (booking.IsInsurance) // Nếu là bảo hiểm
                {
                    // Nếu bệnh viện không tính tiền bảo hiểm
                    if (currentHospitalService.CurrentHospital.IsIgnoreInsurancePayment)
                    {
                        unitPrice = 0;
                    }
                    // Nếu bệnh viện tính tiền bảo hiểm dựa trên giá thu thêm
                    else if (healthService.ExtraPrice.HasValue && healthService.ExtraPrice > 0)
                    {
                        unitPrice = healthService.ExtraPrice ?? 0;
                        data.PaymentAmountDisplay = unitPrice.ToString("N0") + " đ" ?? string.Empty;
                    }
                    else if (healthService.InsurancePrice.HasValue)
                    {
                        // Nếu không có giá thì lấy giá mặc định
                        unitPrice = healthService.InsurancePrice ?? 0;
                        data.PaymentAmountDisplay = healthService.InsurancePriceDisplay ?? string.Empty;

                    }
                }
                data.PaymentAmount = unitPrice;
                data.PaymentAmountDisplay ??= healthService.UnitPriceDisplay ?? unitPrice.ToString("N0") + " đ";
                data.Status = booking.Status;
            }
            else
            {
                var register = await databaseService.Registers
                    .FindAsync(request.Number, cancellationToken);
                if (register == null)
                {
                    result.Set(false, "Không tìm thấy thông tin đăng ký khám", ErrorTypeEnum.MediPayError);
                    return result;
                }
                data = register.Adapt<PatientBookingDto>();
                var customer = await databaseService.Customers
                    .FirstOrDefaultAsync(x => x.Id == register.CustomerId, cancellationToken);
                if (customer != null)
                {
                    data.CustomerId = customer.Id;
                    data.CustomerName = customer.GetFullName();
                    data.CustomerAddress = customer.Address ?? string.Empty;
                    data.WorkAddress = customer.WorkAddress ?? string.Empty;
                    data.EducationLevel = customer.EducationLevel ?? string.Empty;
                    data.CareerId = customer.CareerId ?? string.Empty;
                }

                var payment = await databaseService.Payments
                            .Include(x => x.Receipt)
                            .FirstOrDefaultAsync(x => x.Receipt != null &&
                                                    x.Receipt.RegisterNumber == request.Number,
                                                    cancellationToken);

                data.PaymentNumber = payment?.Id ?? string.Empty;
                data.PaymentStatus = payment?.Status == "PAID";
                data.PaymentAmount = payment?.PaidAmount;
                data.PaymentAmountDisplay = payment?.PaidAmount.ToString("N0") + " đ" ?? string.Empty;
            }
            result.Set(true, "Ok", data);
            return result;
        }
    }
}