﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetRegisters : IRequest<BaseCommandResultWithData<IEnumerable<Register>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? HospitalId { get; set; }
        public List<string>? HealthServiceIds { get; set; } = [];
    }

    public class GetRegistersHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<GetRegisters, BaseCommandResultWithData<IEnumerable<Register>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<IEnumerable<Register>>> Handle(
            GetRegisters request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<Register>>();

            request.FromDate ??= new DateTime(2012, 01, 01);
            request.ToDate ??= DateTime.UtcNow;

            var data = await databaseService.Registers
                .Where(x => x.RegisterAt >= request.FromDate &&
                            x.RegisterAt <= request.ToDate &&
                            (request.HealthServiceIds == null || request.HealthServiceIds.Count == 0 || (x.HealthServiceId != null
                            && request.HealthServiceIds.Contains(x.HealthServiceId))))
                .ToListAsync(cancellationToken: cancellationToken);

            result.Set(true, RegisterConstant.Ok, data);

            return result;
        }
    }
}
