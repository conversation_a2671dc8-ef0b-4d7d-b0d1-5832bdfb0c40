﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using MediTrack.Domain.Helpers;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetRegisterDocument : IRequest<BaseCommandResultWithData<RegisterDocumentDto>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetRegisterDocumentHandler(IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetRegisterDocument, BaseCommandResultWithData<RegisterDocumentDto>>
    {
        public async Task<BaseCommandResultWithData<RegisterDocumentDto>> Handle(
            GetRegisterDocument request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<RegisterDocumentDto>();

            var register = await databaseService.Registers
                .Include(x => x.Customer)
                .Include(x => x.Hospital)
                .FirstOrDefaultAsync(x => x.Number == request.Number, cancellationToken: cancellationToken);

            if(register != null)
            {
                var customerHospital = await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.CustomerId == register.CustomerId && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken: cancellationToken);

                result.Set(true, RegisterConstant.Ok, new RegisterDocumentDto()
                {
                    HospitalName = register.Hospital?.Name ?? string.Empty,
                    Logo = register.Hospital?.LogoUrl ?? string.Empty,
                    RegisterNo = string.Empty,
                    InsuranceNumber = string.Empty,
                    PrimaryHospital = string.Empty,
                    RegisterDate = register.RegisterAt.GetValueOrDefault().ToLocalTime().ToString("dd/MM/yyyy HH:mm"),
                    RegisterNumber = register.Number,
                    ClinicId = register?.ClinicId ?? string.Empty,
                    HealthServiceId = register?.HealthServiceId ?? string.Empty,
                    ClinicName = register?.Clinic ?? string.Empty,
                    HealthServiceName = register?.ServiceName ?? string.Empty,
                    UnitPrice = register?.SubTotalAmount.ToString("N0") ?? "0",
                    CustomerName = register?.Customer?.GetFullName()?.ToUpper() ?? string.Empty,
                    DateOfBirth = register?.Customer?.DateOfBirth,
                    Gender = register?.Customer?.Sex ?? string.Empty,
                    CompanyName = string.Empty,
                    PatientCode = !string.IsNullOrEmpty(register?.PatientCode) 
                        ? register.PatientCode : (customerHospital?.PatientCode ?? string.Empty),
                    Address = register?.Customer?.Address ?? string.Empty,
                    TaxNumber = string.Empty,
                    ServiceName = register?.ServiceName ?? string.Empty,
                    Quantity = 1,
                    HospitalCode = register?.Hospital?.Id ?? string.Empty,
                    HospitalTaxCode = register?.Hospital?.TaxCode ?? string.Empty,
                    LogoUrl = register?.Hospital?.LogoUrl ?? string.Empty,
                    CreateAt = register?.CreatedAt,
                    Priority = register?.QueueNumberPriority == true ? 1 : 0,
                    Clinic = register?.Clinic ?? string.Empty,
                    RefDocNo = register?.RefDocNo ?? string.Empty,
                    ExaminationLocation = register?.ExaminationLocation ?? string.Empty,
                    RateOfInsurance = register?.RateOfInsurance ?? string.Empty,
                    LinkCode = register?.LinkCode ?? string.Empty,
                    PaymentStatus = PaymentConstant.WaitForPayment
                });
            }
            else
            {
                result.Set(false, RegisterConstant.NotFound);
            }
            
            return result;
        }
    }
}
