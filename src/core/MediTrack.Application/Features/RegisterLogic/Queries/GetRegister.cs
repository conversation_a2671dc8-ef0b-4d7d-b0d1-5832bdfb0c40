﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetRegister : IRequest<BaseCommandResultWithData<Register>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetRegisterHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<GetRegister, BaseCommandResultWithData<Register>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<Register>> Handle(
            GetRegister request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Register>();

            var register = await databaseService.Registers
                .FirstOrDefaultAsync(x => x.Number == request.Number, cancellationToken: cancellationToken);

            if(register is null)
            {
                result.Set(false, RegisterConstant.NotFound);
            }
            else
            {
                result.Set(true, RegisterConstant.Ok, register);
            }

            return result;
        }
    }
}
