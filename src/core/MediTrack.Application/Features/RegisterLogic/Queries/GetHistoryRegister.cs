﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetHistoryRegister : IRequest<BaseCommandResultWithData<List<HistoryRegisterDto>>>
    {
        public string? CustomerId { get; set; }
    }

    public class GetHistoryRegisterHandler : IRequestHandler<GetHistoryRegister, BaseCommandResultWithData<List<HistoryRegisterDto>>>
    {
        private readonly ICurrentHospitalService currentHospitalService;
        private readonly IDatabaseService databaseService;

        public GetHistoryRegisterHandler(IDatabaseService databaseService, ICurrentHospitalService currentHospitalService)
        {
            this.databaseService = databaseService;
            this.currentHospitalService = currentHospitalService;
        }

        public async Task<BaseCommandResultWithData<List<HistoryRegisterDto>>> Handle(GetHistoryRegister request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HistoryRegisterDto>>();
            
            var data = await databaseService.Registers
                .Where(x => x.CustomerId == request.CustomerId && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId)
                .Select(x => new HistoryRegisterDto
                {
                    Number = x.Number,
                    RegisterTime = x.RegisterAt ?? DateTime.MinValue,
                    Clinic = x.Clinic,
                    ExameType = x.ExameType,
                    ServiceName = x.ServiceName,
                    HealthInsurance = x.HealthInsurance,
                }).OrderByDescending(x => x.RegisterTime)
                .ToListAsync(cancellationToken: cancellationToken);
            
            if (currentHospitalService.CurrentHospital.IsBlockInsuranceOnWeekend)
            {
                if (DateTimeHelper.GetCurrentLocalDateTime().IsWeekend())
                {
                    data.ForEach(x => x.IsDisabled = x.IsDisabled || x.HealthInsurance == 0);
                }
            }
            result.Set(true, "Ok", data);
            return result;
        }
    }
}