﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Queries;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetHealthServiceByNumber : IRequest<BaseCommandResultWithData<HealthService>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetHealthServiceByNumberHandler(ICachedService cachedService,
        IDatabaseService databaseService,
        IMediator mediator,
        ICurrentHospitalService current) : IRequestHandler<GetHealthServiceByNumber, BaseCommandResultWithData<HealthService>>
    {
        private readonly IHisService hisService = current.HisService;

        public async Task<BaseCommandResultWithData<HealthService>> Handle(
            GetHealthServiceByNumber request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<HealthService>();

            // lấy thông tin phiếu đăng ký
            var register = await databaseService.Registers
                .FirstOrDefaultAsync(x => x.Number == request.Number && x.HospitalId == current.CurrentHospital.HospitalId, cancellationToken);

            if (register is null)
            {
                result.Set(false, RegisterConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            // skip get services, then get services default
            if ((current.CurrentHospital.IsSkipGetInsuranceServices && register.HealthInsurance == 1)
                || (current.CurrentHospital.IsSkipGetServices && register.HealthInsurance == 0))
            {
                var res = await mediator.Send(new GetDefaultHealthServices()
                {
                    IsInsurance = register.HealthInsurance == 1,
                    ClinicId = register.ClinicId,
                    ClinicCode = register.ClinicCode,
                    SubClinicId = register.SubClinicId,
                    ExameTypeId = register.ExameTypeId ?? string.Empty
                }, cancellationToken);
                result.Set(res.Success, res.Messages, res.Data?.Find(x => x.Id == register.HealthServiceId) ?? new HealthService());
                return result;
            }

            (bool getResult, string message, ErrorTypeEnum errorType, List<HealthService> healthServices) =
                    await hisService.GetHealthServices(new GetHealthServicesDto
                    {
                        ExameTypeId = register.ExameTypeId,
                        ClinicId = register.ClinicId,
                        SubClinicId = register.SubClinicId,
                        ClinicCode = register.ClinicCode,
                        KioskId = current.KioskId,
                        HospitalId = current.CurrentHospital.HospitalId
                    });
            Log.Information("{LogPrefix} HIS GetHealthServices  {getResult} {message} {@healthServices}", current.LogPrefix, getResult, message, healthServices);

            foreach (var hs in healthServices)
            {
                await cachedService.SetAsync("HealthServices" + current.CurrentHospital.HospitalId + register.ExameTypeId + hs.Id + register.SubClinicId + register.ClinicCode, hs, cancellationToken);
            }


            var healthService = healthServices.FirstOrDefault(x => x.Id == register.HealthServiceId);
            if (healthService is null)
            {
                result.Set(false, RegisterConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            healthService.ClinicId = register.ClinicId;
            healthService.ClinicCode = register.ClinicCode;
            healthService.SubClinicId = register.SubClinicId;
            healthService.ExameTypeId = register.ExameTypeId;
            result.Set(true, ClinicConstant.Ok, healthService);

            return result;
        }
    }
}
