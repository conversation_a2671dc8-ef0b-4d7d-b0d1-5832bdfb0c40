﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.WardLogic.Queries
{
    public class GetWards
         : IRequest<BaseCommandResultWithData<IEnumerable<WardDto>>>
    {
        public string? Keywords { get; set; }
    }

    public class GetWardsHandler(IWardRepository wardRepository, ICurrentHospitalService currentHospitalService) : IRequestHandler<GetWards, BaseCommandResultWithData<IEnumerable<WardDto>>>
    {
        public async Task<BaseCommandResultWithData<IEnumerable<WardDto>>> Handle(
            
            GetWards request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<WardDto>>();
            var allWards = await wardRepository.GetWardByParentIdAsync(null, currentHospitalService.CurrentHospital.IsTwoLevelAddress, cancellationToken);

            var filteredWards = allWards
                .Where(x => string.IsNullOrEmpty(request.Keywords) || (x.Name != null && x.Name.Contains(request.Keywords)))
                .ToList();
            result.Set(true, DistrictConstant.Ok, filteredWards);

            return result;
        }
    }
}
