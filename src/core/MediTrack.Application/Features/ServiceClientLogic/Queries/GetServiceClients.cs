﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.ServiceClientLogic.Queries;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediatR;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ServiceClientLogic.Queries
{
    public class GetServiceClients : IRequest<BaseCommandResultWithData<IEnumerable<ServiceClient>>>
    {
        public string Keywords { get; set; } = string.Empty;
    }

    public class GetServiceClientsHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        ICachedService cachedService,
        IIdentityService identityService)
        : IRequestHandler<GetServiceClients, BaseCommandResultWithData<IEnumerable<ServiceClient>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ICachedService cachedService = cachedService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResultWithData<IEnumerable<ServiceClient>>> Handle(
            GetServiceClients request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<ServiceClient>>();

            List<ServiceClient>? ServiceClients = await cachedService
                .GetAsync<List<ServiceClient>>("ServiceClients", cancellationToken);

            if (ServiceClients is null)
            {
                ServiceClients = await databaseService.ServiceClients
                    .ToListAsync(cancellationToken);

                await cachedService.SetAsync("ServiceClients", ServiceClients);
            }

            var data = ServiceClients.Where(x => (string.IsNullOrEmpty(request.Keywords) ||
                (x.ClientName != null && x.ClientName.Contains(request.Keywords))));

            result.Set(true, ServiceClientConstant.Ok, data);

            return result;
        }
    }
}
