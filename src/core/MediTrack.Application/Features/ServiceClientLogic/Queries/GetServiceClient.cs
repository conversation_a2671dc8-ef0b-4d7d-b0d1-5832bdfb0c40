﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ServiceClientLogic.Queries
{
    public class GetServiceClient : IRequest<BaseCommandResultWithData<ServiceClient>>
    {
        public GetServiceClient() { }
        public GetServiceClient(Guid id)
        {
            this.Id = id;
        }
        public Guid Id { get; set; }
    }

    public class GetServiceClientHandler(
        IDatabaseService databaseService,
        ICachedService cachedService,
        IIdentityService identityService)
        : IRequestHandler<GetServiceClient, BaseCommandResultWithData<ServiceClient>>
    {
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ICachedService cachedService = cachedService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResultWithData<ServiceClient>> Handle(
            GetServiceClient request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<ServiceClient>();

            List<ServiceClient>? serviceClients = await cachedService
                .GetAsync<List<ServiceClient>>("ServiceClients", cancellationToken);

            if (serviceClients is null)
            {
                serviceClients = await databaseService.ServiceClients
                    .ToListAsync(cancellationToken);

                await cachedService.SetAsync("ServiceClient", serviceClients);
            }

            var serviceClient = serviceClients.FirstOrDefault(x => x.Id == request.Id);

            if (serviceClient is null)
            {
                result.Set(false, ServiceClientConstant.NotFound);
            }
            else
            {
                result.Set(true, ServiceClientConstant.Ok, serviceClient);
            }

            return result;
        }
    }
}
