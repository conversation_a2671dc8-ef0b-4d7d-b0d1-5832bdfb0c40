﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.UserLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Queries
{
    public class GetUsersPaging : IRequest<BaseCommandResultWithData<BasePaging<UserDto>>>
    {
        public string? Keywords { get; set; } = string.Empty;
        public int PageSize { get; set; }
        public int PageIndex { get; set; }

    }

    public class GetUsersPagingHandler(UserManager<User> userManager,
        IDatabaseService db)
        : IRequestHandler<GetUsersPaging, BaseCommandResultWithData<BasePaging<UserDto>>>
    {
        private readonly IDatabaseService db = db;

        public UserManager<User> UserManager { get; } = userManager;

        public async Task<BaseCommandResultWithData<BasePaging<UserDto>>> Handle(
            GetUsersPaging request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<BasePaging<UserDto>>();

            var users = db.Users
                .Where(x => string.IsNullOrEmpty(request.Keywords) || x.UserName!.Contains(request.Keywords));
            var count = await users.CountAsync(cancellationToken: cancellationToken);

            var data = new BasePaging<UserDto>()
            {
                Items = await users.Skip(request.PageIndex * request.PageSize)
                    .Take(request.PageSize)
                    .ProjectToType<UserDto>()
                    .ToListAsync(cancellationToken: cancellationToken),
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                TotalItem = count,
                TotalPage = (count + request.PageSize - 1) / request.PageSize,
            };
            result.Set(true, UserConstant.Ok, data);

            return result;
        }
    }
}
