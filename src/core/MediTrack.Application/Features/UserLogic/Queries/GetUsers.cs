﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.UserLogic.Dtos;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Queries
{
    public class GetUsers : IRequest<BaseCommandResultWithData<IEnumerable<UserDto>>>
    {
        public string? Keywords { get; set; } = string.Empty;
    }
    public class GetUsersHandler(UserManager<User> userManager) : IRequestHandler<GetUsers,
        BaseCommandResultWithData<IEnumerable<UserDto>>>
    {
        private readonly UserManager<User> userManager = userManager;

        public async Task<BaseCommandResultWithData<IEnumerable<UserDto>>> Handle(
            GetUsers request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<UserDto>>();
            var users = await userManager.Users
                .Where(x => string.IsNullOrEmpty(request.Keywords) || x.UserName!.Contains(request.Keywords))
                .ProjectToType<UserDto>()
                .ToListAsync();
            result.Set(true, UserConstant.Ok, users);
            return result;
        }
    }
}
