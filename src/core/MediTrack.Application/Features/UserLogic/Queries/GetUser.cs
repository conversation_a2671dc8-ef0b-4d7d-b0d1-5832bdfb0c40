﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.UserLogic.Dtos;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Queries
{
    public class GetUser : IRequest<BaseCommandResultWithData<UserTransferDto>>
    {
        public string UserName { get; set; } = string.Empty;
    }

    public class GetUserHandler : IRequestHandler<GetUser, BaseCommandResultWithData<UserTransferDto>>
    {
        private readonly UserManager<User> userManager;

        public GetUserHandler(UserManager<User> userManager)
        {
            this.userManager = userManager;
        }
        public async Task<BaseCommandResultWithData<UserTransferDto>> Handle(
            GetUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<UserTransferDto>();

            var user = await userManager.FindByNameAsync(request.UserName);

            if (user is null)
            {
                user = await userManager.FindByEmailAsync(request.UserName);
            }

            if (user != null)
            {
                result.Set(true, UserConstant.Ok, user.Adapt<UserTransferDto>());
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }

            return result;
        }
    }
}
