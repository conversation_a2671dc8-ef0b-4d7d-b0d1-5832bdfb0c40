﻿using FluentValidation;
using MediTrack.Application.Features.UserLogic.Commands;
using MediTrack.Domain.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Validators
{
    public class AuthUserValidator : AbstractValidator<AuthUser>
    {
        public AuthUserValidator()
        {
            RuleFor(x => x.UserName).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage(UserConstant.RequiredUserNameMessage);
            RuleFor(x => x.Password).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage(UserConstant.RequiredPasswordMessage);
        }
    }
}
