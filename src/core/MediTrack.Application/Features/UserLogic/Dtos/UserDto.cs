﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Dtos
{
    public class UserDto
    {
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public string? FullName { get; set; }
        public string? Avatar { get; set; }
        public string? Status { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }

        public string? ResellerId { get; set; }
        public string? ResellerName { get; set; }

        public string? SellerStationId { get; set; }
        public string? SellerStationName { get; set; }
    }
}
