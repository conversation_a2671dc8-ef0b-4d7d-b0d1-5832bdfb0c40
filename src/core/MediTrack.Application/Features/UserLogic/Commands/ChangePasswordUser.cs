﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class ChangePasswordUser : IRequest<BaseCommandResult>
    {
        public string OldPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }
    public class ChangePasswordUserHandler(UserManager<User> userManager,
        ICurrentUserService currentUserService)
        : IRequestHandler<ChangePasswordUser, BaseCommandResult>
    {
        private readonly UserManager<User> userManager = userManager;
        private readonly ICurrentUserService currentUserService = currentUserService;

        public async Task<BaseCommandResult> Handle(
            ChangePasswordUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            if(string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, IdentityConstant.Forbidden);
                return result;
            }    

            var user = await userManager.FindByNameAsync(currentUserService.UserName);

            if (user is null)
            {
                user = await userManager.FindByEmailAsync(currentUserService.UserName);
            }

            if (user != null)
            {
                var isValidPassword = await userManager.CheckPasswordAsync(user, request.OldPassword);

                if (isValidPassword)
                {
                    var changePasswordToken = await userManager
                        .GeneratePasswordResetTokenAsync(user);
                    var changePasswordResult = await userManager
                        .ChangePasswordAsync(user, request.OldPassword, request.NewPassword);

                    if (changePasswordResult.Succeeded)
                    {
                        result.Set(true, UserConstant.ChangePasswordSuccess);
                    }
                    else
                    {
                        var errors = changePasswordResult.Errors.Select(x => new BaseCommandError()
                        {
                            Code = x.Code,
                            Message = x.Description
                        });
                        result.Errors = errors.ToList();
                        result.Set(false, UserConstant.ChangePasswordFail);
                    }
                }
                else
                {
                    result.Messages = UserConstant.InvalidPassword;
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }
            return result;
        }
    }

}
