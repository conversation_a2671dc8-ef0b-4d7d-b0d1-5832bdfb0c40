﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class RefreshTokenUser : IRequest<BaseCommandResultWithData<User>>
    {
        public string RefreshToken { get; set; } = string.Empty;
    }

    public class RefreshTokenUserHandler(IDatabaseService databaseService,
       UserManager<User> userManager)
               : IRequestHandler<RefreshTokenUser, BaseCommandResultWithData<User>>
    {
        public async Task<BaseCommandResultWithData<User>> Handle(
            RefreshTokenUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<User>();

            var refreshToken = databaseService.RefreshTokens
                .FirstOrDefault(x => x.Token == request.RefreshToken);

            if (refreshToken is not null)
            {
                if (DateTime.UtcNow > refreshToken.ExpiresAt)
                {
                    result.Set(false, "Token is expired");
                }
                else
                {
                    var user = await userManager
                        .FindByIdAsync(refreshToken.UserId?.ToString() ?? string.Empty);

                    if (user != null &&
                        user.IsActive == true)
                    {
                        result.Set(false, "Invalid user");
                        return result;
                    }

                    refreshToken.ExpiresAt = DateTime.UtcNow;
                    databaseService.RefreshTokens.Update(refreshToken);

                    var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                    if (saveResult > 0)
                    {
                        result.Set(true, "Ok", user ?? new User());
                    }
                    else
                    {
                        result.Set(true, "Internal error");
                    }
                }
            }
            else
            {
                result.Set(false, "Invalid refresh token");
            }

            return result;
        }
    }
}
