﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class AddUserToken : IRequest<BaseCommandResult>
    {
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
    }

    public class AddUserTokenHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IIdentityService identityService)
        : IRequestHandler<AddUserToken, BaseCommandResult>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResult> Handle(AddUserToken request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var userTokens = new RefreshToken()
            {
                Token = request.RefreshToken,
                JwtId = request.Token,
                UserId = request.UserId,
                GeneratedAt = DateTime.Now,
                CreatedByIp = currentUserService.UserHost,
                ExpiresAt = DateTime.Now.AddDays(7),
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
            };
            databaseService.RefreshTokens.Add(userTokens);
            var saveChangeResult = await databaseService
                .SaveChangesAsync(cancellationToken);

            if (saveChangeResult > 0)
            {
                result.Success = true;
                result.Messages = UserConstant.SaveChangesSuccess;
            }
            else
            {
                result.Success = false;
                result.Messages = UserConstant.SaveChangesError;
            }

            return result;
        }
    }
}
