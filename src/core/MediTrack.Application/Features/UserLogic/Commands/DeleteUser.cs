﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class DeleteUser : IRequest<BaseCommandResult>
    {
        public string UserName { get; set; } = string.Empty;
    }

    public class DeleteUserHandler(UserManager<User> userManager) : IRequestHandler<DeleteUser, BaseCommandResult>
    {
        private readonly UserManager<User> userManager = userManager;

        public async Task<BaseCommandResult> Handle(DeleteUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var user = await userManager.FindByNameAsync(request.UserName);

            if (user is null)
            {
                user = await userManager.FindByEmailAsync(request.UserName);
            }

            if (user != null)
            {
                user.IsActive = false;
                var updateResult = await userManager.UpdateAsync(user);

                if (!updateResult.Succeeded)
                {
                    result.Success = false;
                    result.Messages = UserConstant.SaveChangesError;
                    result.Errors = updateResult.Errors
                        .Select(x => new BaseCommandError()
                        {
                            Code = x.Code,
                            Message = x.Description
                        });
                }
                else
                {
                    result.Success = true;
                    result.Messages = UserConstant.SaveChangesSuccess;
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }

            return result;
        }
    }
}
