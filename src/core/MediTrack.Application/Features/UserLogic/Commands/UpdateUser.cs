﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class UpdateUser : IRequest<BaseCommandResult>
    {
        public string UserName { get; set; } = string.Empty;
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Avatar { get; set; }
        public string? ResellerId { get; set; }
        public string? SellerStationId { get; set; }
    }
    public class UpdateUserHandler : IRequestHandler<UpdateUser, BaseCommandResult>
    {
        private readonly UserManager<User> userManager;

        public UpdateUserHandler(UserManager<User> userManager)
        {
            this.userManager = userManager;
        }
        public async Task<BaseCommandResult> Handle(
            UpdateUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var user = await userManager.FindByNameAsync(request.UserName);

            user ??= await userManager.FindByEmailAsync(request.UserName);
            user ??= await userManager.Users
                .FirstOrDefaultAsync(u => u.PhoneNumber == request.UserName);

            if (user != null)
            {
                user.Email = request.Email;
                user.PhoneNumber = request.PhoneNumber;
                user.FullName = request.FullName ?? request.UserName!;
                user.Avatar = request.Avatar;

                var updateResult = await userManager.UpdateAsync(user);

                if (!updateResult.Succeeded)
                {
                    result.Success = false;
                    result.Messages = UserConstant.SaveChangesError;
                    result.Errors = updateResult.Errors
                        .Select(x => new BaseCommandError()
                        {
                            Code = x.Code,
                            Message = x.Description
                        });
                }
                else
                {
                    result.Success = true;
                    result.Messages = UserConstant.SaveChangesSuccess;
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }

            return result;
        }
    }
}
