﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class AssignRolesToUser : IRequest<BaseCommandResult>
    {
        public string UserName { get; set; } = string.Empty;
        public IEnumerable<string> Roles { get; set; } = new List<string>();
    }

    public class AssignRolesHandler
        : IRequestHandler<AssignRolesToUser, BaseCommandResult>
    {
        private readonly UserManager<User> userManager;

        public AssignRolesHandler(UserManager<User> userManager)
        {
            this.userManager = userManager;
        }
        public async Task<BaseCommandResult> Handle(
            AssignRolesToUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var user = await userManager.FindByNameAsync(request.UserName);

            if (user is null)
            {
                user = await userManager.FindByEmailAsync(request.UserName);
            }

            if (user != null)
            {
                var oldRoles = await userManager.GetRolesAsync(user);

                var asignRoleResult = await userManager.RemoveFromRolesAsync(user, oldRoles);
                asignRoleResult = await userManager.AddToRolesAsync(user, request.Roles);

                if (!asignRoleResult.Succeeded)
                {
                    result.Success = false;
                    result.Messages = UserConstant.SaveChangesError;
                    result.Errors = asignRoleResult.Errors
                        .Select(x => new BaseCommandError()
                        {
                            Code = x.Code,
                            Message = x.Description
                        });
                }
                else
                {
                    result.Success = true;
                    result.Messages = UserConstant.SaveChangesSuccess;
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }


            return result;
        }
    }
}
