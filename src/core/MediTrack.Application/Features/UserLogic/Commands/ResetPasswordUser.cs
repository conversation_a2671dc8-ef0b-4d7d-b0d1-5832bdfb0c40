﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class ResetPasswordUser : IRequest<BaseCommandResult>
    {
        public string UserName { get; set; } = string.Empty;
        public string ResetPassword { get; set; } = string.Empty;
    }

    public class ResetPasswordUserHandler : IRequestHandler<ResetPasswordUser, BaseCommandResult>
    {
        private readonly UserManager<User> userManager;
        private readonly SignInManager<User> signInManager;

        public ResetPasswordUserHandler(UserManager<User> userManager,
            SignInManager<User> signInManager)
        {
            this.userManager = userManager;
            this.signInManager = signInManager;
        }
        public async Task<BaseCommandResult> Handle(ResetPasswordUser request,
            CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();
            var user = await userManager.FindByNameAsync(request.UserName);

            user ??= await userManager.FindByEmailAsync(request.UserName);
            user ??= await userManager.Users
                .FirstOrDefaultAsync(u => u.PhoneNumber == request.UserName);

            if (user != null)
            {
                var resetToken = await userManager.GeneratePasswordResetTokenAsync(user);
                var changePasswordResult = await userManager
                    .ResetPasswordAsync(user, request.ResetPassword, resetToken);

                if (changePasswordResult.Succeeded)
                {
                    result.Set(true, UserConstant.Ok);
                }
                else
                {
                    result.Set(false, UserConstant.SaveChangesError);
                    result.Errors = changePasswordResult.Errors.Select(x => new BaseCommandError()
                    {
                        Code = x.Code,
                        Message = x.Description,
                    });
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }

            return result;
        }
    }
}
