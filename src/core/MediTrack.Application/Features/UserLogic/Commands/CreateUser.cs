﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.UserLogic.Dtos;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class CreateUser : IRequest<BaseCommandResultWithData<UserDto>>
    {
        public string? UserName { get; set; }
        public string? FullName { get; set; }
        public string? Password { get; set; }
        public string? ConfirmPassword { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Avatar { get; set; }
    }

    public class CreateUserHandler : IRequestHandler<CreateUser,
        BaseCommandResultWithData<UserDto>>
    {
        private readonly UserManager<User> userManager;

        public CreateUserHandler(UserManager<User> userManager)
        {
            this.userManager = userManager;
        }

        public async Task<BaseCommandResultWithData<UserDto>> Handle(
            CreateUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<UserDto>();

            var user = new User()
            {
                UserName = request.UserName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                FullName = request.FullName ?? request.UserName!,
                Avatar = request.Avatar
            };
            var createUserResult = await userManager
                .CreateAsync(user, request.Password ?? UserConstant.DefaultPassword);

            if (createUserResult.Succeeded)
            {
                result.Success = true;
                result.Messages = UserConstant.SaveChangesSuccess;
                result.Data = user.Adapt<UserDto>();
            }
            else
            {
                result.Success = false;
                result.Messages = UserConstant.SaveChangesError;
                result.Errors = createUserResult.Errors
                    .Select(x => new BaseCommandError()
                    {
                        Code = x.Code,
                        Message = x.Description
                    });
            }

            return result;
        }
    }
}
