using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.TransferReferralLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.TransferReferralLogic.Queries
{
    public class GetTransferReferralReasons : IRequest<BaseCommandResultWithData<List<TransferReferralReason>>>
    {
    }

    public class GetTransferReferralReasonsHandler(IHospitalMetadataRepository hospitalMetadataRepository,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetTransferReferralReasons, BaseCommandResultWithData<List<TransferReferralReason>>>
    {
        public async Task<BaseCommandResultWithData<List<TransferReferralReason>>> Handle(
            GetTransferReferralReasons request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<TransferReferralReason>>();

            //1. L<PERSON>y thông tin phiên bản hiện tại của kiosk từ database
            var systemMetaData = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "transfer_referral_reason");
            if (systemMetaData != null && !string.IsNullOrEmpty(systemMetaData.Value))
            {
                List<TransferReferralReason> transferReferralReasons = JsonConvert.DeserializeObject<List<TransferReferralReason>>(systemMetaData.Value) ?? new List<TransferReferralReason>();
                result.Set(true, "", transferReferralReasons);
            }
            else
            {
                result.Set(false, "Không tìm thấy dữ liệu", []);
            }
            return result;
        }
    }
}