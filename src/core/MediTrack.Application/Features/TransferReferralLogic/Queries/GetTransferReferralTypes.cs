using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.TransferReferralLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.TransferReferralLogic.Queries
{
    public class GetTransferReferralTypes : IRequest<BaseCommandResultWithData<List<TransferReferralType>>>
    {
    }

    public class GetTransferReferralTypesHandler(IHospitalMetadataRepository hospitalMetadataRepository,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetTransferReferralTypes, BaseCommandResultWithData<List<TransferReferralType>>>
    {
        public async Task<BaseCommandResultWithData<List<TransferReferralType>>> Handle(
            GetTransferReferralTypes request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<TransferReferralType>>();

            //1. <PERSON><PERSON><PERSON> thông tin phiên bản hiện tại của kiosk từ database
            var systemMetaData = await hospitalMetadataRepository
                .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "transfer_referral_type", cancellationToken: cancellationToken);

            if (systemMetaData != null && !string.IsNullOrEmpty(systemMetaData.Value))
            {
                List<TransferReferralType> transferReferralTypes = JsonConvert.DeserializeObject<List<TransferReferralType>>(systemMetaData.Value) ?? [];
                result.Set(true, "", transferReferralTypes);
            }
            else
            {
                result.Set(false, "Không tìm thấy dữ liệu", []);
            }
            return result;
        }
    }
}