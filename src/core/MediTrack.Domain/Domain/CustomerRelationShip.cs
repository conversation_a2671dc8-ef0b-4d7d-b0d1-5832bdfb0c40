﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class CustomerRelationShip : BaseAudit
    {
        public string CustomerId { get; set; } = string.Empty;
        public string CustomerIdRefer { get; set; } = string.Empty;
        public string RelationshipId { get; set; } = string.Empty;

        public virtual Customer? Customer { get; set; }
        public virtual Relationship? Relationship { get; set;} 
    }
}