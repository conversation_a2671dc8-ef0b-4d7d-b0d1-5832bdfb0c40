﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON>ố thứ tự khám bệnh
    /// </summary>
    public class DialerQueue : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Mã loại khám
        /// </summary>
        public string? ExameTypeId { get; set; }
        /// <summary>
        /// Mã phòng khám/Chuyên khoa
        /// </summary>
        public string? ClinicId { get; set; }
        /// <summary>
        /// Mã phòng khám/dịch vụ
        /// </summary>
        public string? HealthServiceId { get; set; } 
        /// <summary>
        /// Là số thứ tự của bệnh nhân ưu tiên
        /// </summary>
        public bool IsPriority { get; set; }
        /// <summary>
        /// Ngày lấy số thứ tự
        /// </summary>
        public DateTime QueueDate { get; set; } = DateTime.UtcNow;
        /// <summary>
        /// <PERSON><PERSON> thứ tự
        /// </summary>
        public int QueueNumber { get; set; }
        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public string HospitalId { get; set; } = string.Empty;

        public virtual Hospital? Hospital { get; set; }
    }
}
