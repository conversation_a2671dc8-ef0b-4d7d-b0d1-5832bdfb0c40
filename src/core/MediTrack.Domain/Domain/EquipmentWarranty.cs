using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class EquipmentWarranty : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string? EquipmentName { get; set; }
        public string? EquipmentId { get; set; }
        public string? EquipmentModelId { get; set; }
        public string? Serial { get; set; }
        public EquipmentModel? EquipmentModel { get; set; }
        public DateTime? WarrantyStartDate { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
    }
}
