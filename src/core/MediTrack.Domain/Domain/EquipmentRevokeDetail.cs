using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class EquipmentRevokeDetail : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string EquipmentCatalogId { get; set; } = string.Empty;
        public EquipmentCatalog? EquipmentCatalog { get; set; }
        public int? Quantity { get; set; }
        public string Serial {get; set;} = string.Empty;

        public string? EquipmentRevokeId { get; set; }
    }
}
