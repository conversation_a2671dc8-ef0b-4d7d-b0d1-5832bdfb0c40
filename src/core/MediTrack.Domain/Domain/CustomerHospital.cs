﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class CustomerHospital : BaseAudit
    {
        public string CustomerId { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        /// <summary>
        /// Id bệnh nhân theo HIS
        /// </summary>
        public string PatientId { get; set;} = string.Empty;
        /// <summary>
        /// Mã bệnh nhân theo HIS
        /// </summary>
        public string PatientCode { get; set;} = string.Empty;
        /// <summary>
        /// Mã nghề nghiệp theo HIS
        /// </summary>
        public string CareerId { get; set; } = string.Empty;
        public virtual Customer Customer { get; set; } = null!;
        public virtual Hospital Hospital { get; set; } = null!;

        /// <summary>
        /// Đ<PERSON>h dấu yêu thích
        /// </summary>
        public bool IsFavourite {get; set; } = false;
        /// <summary>
        /// Review
        /// </summary>
        public string? Review {get; set; } 
        /// <summary>
        /// Điể<PERSON> đán<PERSON> giá
        /// </summary>
        public double? Rating {get; set; }
    }
}