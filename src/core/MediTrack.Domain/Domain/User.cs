﻿using Microsoft.AspNetCore.Identity;

namespace MediTrack.Domain.Domain
{
    public class User : IdentityUser<string>
    {
        public string? CustomerId { get; set; }        
        public string? FullName { get; set; } = string.Empty;
        public string? Avatar { get; set; } = string.Empty;
        public bool? IsActive { get; set; }
        /// <summary>
        /// 1: Admin, 2: Patient
        /// </summary>
        public int UserType { get; set; } = 2;
        public string? CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; } = string.Empty;
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsFirstLogin { get; set; }
        public bool? IseKYC { get; set; }

        public virtual List<Role>? Roles { get; set; } = [];
        public virtual List<HospitalUser>? HospitalUsers { get; set; } = [];
    }
}
