using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Đối tác quảng cáo - entity lưu thông tin của đối tác quảng cáo
    /// </summary>
    public class AdvertisingPartner : BaseAudit
    {
        /// <summary>
        /// Định danh duy nhất của đối tác quảng cáo
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// Tên đối tác quảng cáo
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Mã đối tác
        /// </summary>
        public string PartnerCode { get; set; } = string.Empty;
        
        /// <summary>
        /// Số hợp đồng
        /// </summary>
        public string ContractNo { get; set; } = string.Empty;
        
        /// <summary>
        /// Ngày ký kết
        /// </summary>
        public DateTime ContractDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Khu vực
        /// </summary>
        public string Zone { get; set; } = string.Empty;
        
        /// <summary>
        /// Tên người đại diện
        /// </summary>
        public string RepresentativeName { get; set; } = string.Empty;
        
        /// <summary>
        /// Số điện thoại liên hệ
        /// </summary>
        public string Phone { get; set; } = string.Empty;
        
        /// <summary>
        /// Địa chỉ liên hệ
        /// </summary>
        public string? Address { get; set; }
        
        /// <summary>
        /// Email liên hệ
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Số lượng media tối đa có thể tải lên trong một chiến dịch
        /// </summary>
        public int MaxMediaPerCampaign { get; set; } = 1;
        
        /// <summary>
        /// Cờ xác định đối tác có đang hoạt động hay không
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Danh sách các chiến dịch mà đối tác tham gia
        /// </summary>
        public virtual ICollection<AdvertisingPartnerCampaign> CampaignParticipations { get; set; } = [];
    }
}