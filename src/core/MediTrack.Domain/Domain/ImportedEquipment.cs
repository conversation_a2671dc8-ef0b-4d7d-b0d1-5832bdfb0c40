using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ImportedEquipment : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string? Title { get; set; }
        // kho
        public string? WarehousePartnerId { get; set; }
        public WarehousePartner? WarehousePartner { get; set; }
        public string? WarehousePartnerImportId { get; set; }
        public WarehousePartner? WarehousePartnerImport { get; set; }

        // Trạng thái
        public string Status { get; set; } = string.Empty;
        // Người thao tác
        public string? ImportedAccount { get; set; }
        public string? ImportedAddress { get; set; }
        public string? ImportedPhone { get; set; }
        public DateTime? ImportedAt { get; set; }

        // Ghi chú
        public string? Note { get; set; }

        // Nhà cung cấp
        public string? EquipmentVendorId { get; set; }
        public EquipmentVendor? EquipmentVendor { get; set; }
        public string? Phone { get; set; }

        public List<ImportedEquipmentDetail>? ImportedEquipmentDetails { get; set; }
        public List<ImportedEquipmentHistory>? ImportedEquipmentHistories { get; set; }
    }
}
