﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class CustomerReview : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public string Problem { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        public string? CustomerId { get; set; }
        public string? TypeId { get; set; }
        /// <summary>
        /// <PERSON><PERSON> danh sách các dịch vụ của góp ý
        /// </summary>
        [Column(TypeName = "jsonb")]
        public List<string>? ServiceIds { get; set; }
        public virtual Hospital? Hospital { get; set; }
        public virtual Customer? Customer { get; set; }
    }
}
