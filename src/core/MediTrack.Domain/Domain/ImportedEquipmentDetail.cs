using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ImportedEquipmentDetail : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string? EquipmentCatalogId { get; set; }
        public EquipmentCatalog? EquipmentCatalog { get; set; }
        public int? Quantity { get; set; }
        public string Serial { get; set; } = string.Empty;

        public string? ImportedEquipmentId { get; set; }
    }
}
