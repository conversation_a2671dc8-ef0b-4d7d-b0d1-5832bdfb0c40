using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class EquipmentModel : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? EquipmentTypeCode { get; set; }
        public string? EquipmentTypeName { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
    }
}
