﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin chi tiết phiếu đăng kí
    /// </summary>
    public class RegisterDetail : BaseAudit
    {
        /// <summary>
        /// Id chi tiết phiếu đăng kí
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Id đơn hàng
        /// </summary>
        public string? RegisterNumber { get; set; }
        /// <summary>
        /// Mã dịch vụ khám chữa bệnh
        /// </summary>
        public string HealthServiceId { get; set; } = string.Empty;
        /// <summary>
        /// Id sản phẩm
        /// </summary>
        public string ServiceCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string ServiceName { get; set; } = string.Empty;
        /// <summary>
        /// Mã tiếp nhận tích hợp với His
        /// </summary>
        public string RefNo { get; set; } = string.Empty;
        /// <summary>
        /// Số lượng
        /// </summary>
        public int Quantity { get; set; } = 1;
        /// <summary>
        /// Giá dịch vụ (VND)
        /// </summary>
        public decimal UnitPrice { get; set; }
        /// <summary>
        /// Đơn vị tiền tệ: VND, USD,..
        /// </summary>
        public string Currency { get; set; } = "VND";
        /// <summary>
        /// Giá tiền theo đơn vị tiền tệ
        /// </summary>
        public decimal UnitPriceOfCurrency { get; set; }
        /// <summary>
        /// Tỉ lệ chuyển đổi tiền tệ
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;
        /// <summary>
        /// Tổng giá tiền dịch vụ sau thuế (thanh toán) (VND) bằng SubTotalAmount - DiscountAmount
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;
        /// <summary>
        /// Thông tin đính kèm đăng kí
        /// </summary>
        public string CustomerName { get; set; } = string.Empty;
        /// <summary>
        /// Giới tính
        /// </summary>
        public string CustomerSex { get; set; } = string.Empty;
        /// <summary>
        /// Ngày tháng năm sinh
        /// </summary>
        public DateTime? DateOfBirth { get; set; }
        /// <summary>
        /// Số căn cước công dân
        /// </summary>
        public string IdentityNo { get; set; } = string.Empty;
        /// <summary>
        /// Địa chỉ
        /// </summary>
        public string CustomerAddress { get; set; } = string.Empty;
        /// <summary>
        /// Dân tộc
        /// </summary>
        public string? NationalityId { get; set; }
        /// <summary>
        /// Customer phone
        /// </summary>
        public string CustomerPhone { get; set; } = string.Empty;
        /// <summary>
        /// Giá gốc của dịch vụ trước áp dụng khuyến mãi bằng đơn giá nhân với số lượng
        /// </summary>
        public decimal SubTotalAmount { get; set; } = 0;
        /// <summary>
        /// Số tiền được giảm 
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;
        /// <summary>
        /// Giá trị khuyến mãi khi mua dịch vụ
        /// </summary>
        public decimal DiscountValue { get; set; } = 0;
        /// <summary>
        /// Đơn vị của khuyến mãi khi mua dịch vụ (% - PERCENT, số tiền - MONEY,...)
        /// </summary>
        public string? DiscountUnit { get; set; }
        /// <summary>
        /// Số thứ tự vào phòng khám
        /// </summary>
        public int? QueueNumber { get; set; }
        /// <summary>
        /// Vị trí khám
        /// </summary>
        public string ExaminationLocation { get; set; } = string.Empty;
        /// <summary>
        /// Trạng thái
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// Id phòng khám
        /// </summary>
        public string ClinicId { get; set; } = string.Empty;
        /// <summary>
        /// Mã phòng khám
        /// </summary>
        public string ClinicCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên phòng khám
        /// </summary>
        public string Clinic { get; set; } = string.Empty;
        /// <summary>
        /// Mã loại khám (Bảo hiểm, dịch vụ,..)
        /// </summary>
        public string? ExameTypeId { get; set; } = string.Empty;
        /// <summary>
        /// Tên loại khám
        /// </summary>
        public string ExameType { get; set; } = string.Empty;
    }
}