using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class Otp : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public bool Current { get; set; } = false;
        public bool Used { get; set; } = false;
        public bool Success { get; set; } = false;
    }
}
