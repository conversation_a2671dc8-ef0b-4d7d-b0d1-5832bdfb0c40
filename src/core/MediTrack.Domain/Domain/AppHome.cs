using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class AppHome : BaseAudit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Title { get; set; }
        public string? Value { get; set; }
    }
}