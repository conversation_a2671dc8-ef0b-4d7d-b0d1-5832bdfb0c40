﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class Payment : BaseAudit
    {
        /// <summary>
        /// Mã thanh toán tại Meditrack
        /// </summary>
        public string Id { get; set; } = string.Empty;

        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// Tiền yêu cầu thanh toán
        /// </summary>
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// Số tiền đã thanh toán
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// Trạng thái thanh toán
        /// </summary>
        public string? Status { get; set; } = string.Empty;

        /// <summary>
        /// Message phản hồi từ API thanh toán (HIS)
        /// </summary>
        public string? Message { get; set; } = string.Empty;

        /// <summary>
        /// QRCode
        /// </summary>
        public string? QrCode { get; set; } = string.Empty;

        /// <summary>
        /// S<PERSON> phiếu thu (hệ thống Meditrack)
        /// </summary>
        public string? ReceiptNumber { get; set; } = string.Empty;

        /// <summary>
        /// Mã tham chiếu thanh toán (HIS)
        /// </summary>
        public string? RefNo { get; set; } = string.Empty;

        /// <summary>
        /// Mã tham chiếu thanh toán (BANK)
        /// </summary>
        public string? RefTran { get; set; } = string.Empty;

        /// <summary>
        /// Nội dung chuyển khoản (request)
        /// </summary>
        public string? RefDescReq { get; set; } = string.Empty;

        /// <summary>
        /// Nội dung chuyển khoản (thực tế)
        /// </summary>
        public string? RefDesc { get; set; } = string.Empty;

        /// <summary>
        /// Trạng thái gửi API IPN qua his
        /// </summary>
        public string? IpnStatus { get; set; } = string.Empty;

        /// <summary>
        /// IPN của luồng tự gen
        /// </summary>
        public string? IpnUrl { get; set; } = string.Empty;

        /// <summary>
        /// Phản hồi từ IPN
        /// </summary>
        public string? IpnMessage { get; set; } = string.Empty;

        /// <summary>
        /// Transaction ID của QR
        /// </summary>
        public string? QrTransactionId { get; set; } = string.Empty;

        /// <summary>
        /// Mã hóa đơn điện tử của HIS (Đà Nẵng), phieuThuId của VNPT
        /// </summary>
        public string? InvoiceInfoRef { get; set; } = string.Empty;

        /// <summary>
        /// Payment genQR từ Meditrack hay không
        /// </summary>
        public bool IsHisGenQr { get; set; } = false;

        /// <summary>
        /// Id bệnh viện
        /// </summary>
        public string? HospitalId { get; set; }

        /// <summary>
        /// Loại thanh toán: QR, POS...
        /// </summary>
        public string PaymentMethod { get; set; } = "QR";

        /// <summary>
        /// Request báo có sang đối tác
        /// </summary>
        public string? RequestToPartner { get; set; }

        /// <summary>
        /// Response từ đối tác
        /// </summary>
        public string? ResponseFromPartner { get; set; }

        public virtual Receipt? Receipt { get; set; }
        public virtual Hospital? Hospital { get; set; }
    }
}