using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Chiến dịch quảng cáo 
    /// </summary>
    public class AdvertisingCampaign : BaseAudit
    {
        /// <summary>
        /// Định danh duy nhất của chiến dịch quảng cáo
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// Tên chiến dịch quảng cáo
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Mô tả về chiến dịch quảng cáo
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// Ngày khởi tạo chiến dịch quảng cáo
        /// </summary>
        public DateTime CreationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Ngày bắt đầu chiến dịch quảng cáo
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Ng<PERSON>y kết thúc chiến dịch quảng cáo (có thể để trống để đánh dấu vô hạn)
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// Thời gian bắt đầu hiển thị quảng cáo trong ngày (null nếu hiển thị cả ngày)
        /// </summary>
        public TimeSpan? DailyStartTime { get; set; }
        
        /// <summary>
        /// Thời gian kết thúc hiển thị quảng cáo trong ngày (null nếu hiển thị cả ngày)
        /// </summary>
        public TimeSpan? DailyEndTime { get; set; }
        
        /// <summary>
        /// Cờ xác định chiến dịch có đang hoạt động hay không
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Danh sách các đối tác tham gia chiến dịch
        /// </summary>
        public virtual ICollection<AdvertisingPartnerCampaign> PartnerParticipations { get; set; } = [];
        
        /// <summary>
        /// Danh sách các kiosk tham gia chiến dịch
        /// </summary>
        public virtual ICollection<AdvertisingKioskCampaign> KioskParticipations { get; set; } = [];
    }
}