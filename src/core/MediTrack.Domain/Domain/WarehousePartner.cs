using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class WarehousePartner : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
    }
}
