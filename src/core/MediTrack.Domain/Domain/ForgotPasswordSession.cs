using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ForgotPasswordSession : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// otp expired time
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        /// <summary>
        /// Update this field when user verify otp
        /// </summary>
        public DateTime? VerifiedAt { get; set; }

        public string UserId { get; set; } = string.Empty;
        public User? User { get; set; }
    }
}