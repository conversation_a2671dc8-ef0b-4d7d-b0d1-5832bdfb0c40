﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Danh sách dịch vụ khám, ch<PERSON><PERSON> bệnh theo khoa
    /// </summary>
    public class HealthService : BaseAudit
    {
        /// <summary>
        /// Id dịch vụ
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Code dịch vụ
        /// </summary>
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// Tên dịch vụ
        /// </summary>
        public string Name { get; set; } = string.Empty;
        /// <summary>
        /// Id chuyên khoa
        /// </summary>
        public string? ClinicId { get; set; }
        /// <summary>
        /// Mã chuyên khoa (BV Đức Giang có mã chuyên khoa)
        /// </summary>
        public string? ClinicCode { get; set; }
        /// <summary>
        /// Id chuyên khoa response từ HIS (BV 354)
        /// </summary>
        public string? ClinicIdRes { get; set; }
        /// <summary>
        /// Mã chuyên khoa response từ HIS (BV 354)
        /// </summary>
        public string? ClinicCodeRes { get; set; }
        /// <summary>
        /// Id nhóm phòng khám
        /// </summary>
        public string? ClinicGroupId { get; set; }
        /// <summary>
        /// Mã nhóm phòng khám
        /// </summary>
        public string? ClinicGroupCode { get; set; }
        /// <summary>
        /// Tên nhóm phòng khám
        /// </summary>
        public string? ClinicGroup { get; set; }
        /// <summary>
        /// Id chuyên khoa con
        /// </summary>
        public string? SubClinicId { get; set; }
        /// <summary>
        /// Giá dịch vụ (VNĐ)
        /// </summary>
        public decimal? UnitPrice { get; set; }
        /// <summary>
        /// Giá hiển thị  dịch vụ (giá hiển thị với giá thực tế là khác nhau)
        /// Ví dụ InsurancePrice = 100000, InsurancePriceDisplay = 'Bảo hiểm chi trả 100%'
        /// </summary>
        public string? UnitPriceDisplay { get; set; }
        /// <summary>
        /// Chuyên khoa
        /// </summary>
        public virtual Clinic? Clinic { get; set; }
        /// <summary>
        /// Giá bảo hiểm (VNĐ)
        /// </summary>
        public decimal? InsurancePrice { get; set; }
        /// <summary>
        /// Giá thu thêm
        /// </summary>
        public decimal? ExtraPrice { get; set; }
        /// <summary>
        /// Giá hiển thị  bảo hiểm (giá hiển thị với giá thực tế là khác nhau)
        /// Ví dụ InsurancePrice = 100000, InsurancePriceDisplay = 'Bảo hiểm chi trả 100%'
        /// </summary>
        public string? InsurancePriceDisplay { get; set; }
        /// <summary>
        /// Loại dịch vụ khám
        /// </summary>
        public string? ExameTypeId { get; set; }
        /// <summary>
        /// Loại dịch vụ khám response từ HIS (BV 354)
        /// </summary>
        public string? ExameTypeIdRes { get; set; }
        /// <summary>
        /// Có bỏ qua thanh toán hay không (dùng cho bảo hiểm)
        /// </summary>
        public bool IsIgnoreInsurancePayment { get; set; }
        /// <summary>
        /// Nơi khám bệnh (Phòng A1, Tầng.....)
        /// </summary>
        public string? ExaminationLocation { get; set; }
        /// <summary>
        /// Thứ tự sắp xếp
        /// </summary>
        public int SortIndex { get; set; }
        /// <summary>
        /// Thời gian khám bệnh
        /// </summary>
        public string? ExaminationHour { get; set; }
        /// <summary>
        /// Số lượng bệnh nhân đang chờ khám
        /// </summary>
        public int? WaitingPatientCount { get; set; }
        /// <summary>
        /// Số lượng bệnh nhân khám còn lại
        /// </summary>
        public int? RemainingPatientCount { get; set; }
        /// <summary>
        /// Số lượng bệnh nhân tổng cộng
        /// </summary>
        public int? TotalPatientCount { get; set; }
        /// <summary>
        /// STT bệnh nhân đang khám
        /// </summary>
        public int? ProcessingNumber { get; set; }
        /// <summary>
        /// Đơn vị tiền tệ: VND, USD,..
        /// </summary>
        public string? Currency { get; set; } = "VND";
        /// <summary>
        /// Giá tiền theo đơn vị tiền tệ, bằng với UnitPrice nếu Currency = VND
        /// </summary>
        public decimal? UnitPriceOfCurrency { get; set; }
        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public string? HospitalId { get; set; }
    }
}
