﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Phiếu đăng ký khám chữa bệnh
    /// </summary>
    public class Register : BaseAudit
    {
        /// <summary>
        /// Số phiếu tại Meditrack
        /// </summary>
        public string Number { get; set; } = string.Empty;
        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// Mã thành viên
        /// </summary>
        public string? MembershipId { get; set; }

        /// <summary>
        /// Mã người thân
        /// </summary>
        public string? CustomerRelationId { get; set; }
        /// <summary>
        /// Mã dịch vụ khám chữa bệnh
        /// </summary>
        public string? HealthServiceId { get; set; }
        /// <summary>
        /// Id sản phẩm
        /// </summary>
        public string ServiceCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên dịch vụ
        /// </summary>
        public string ServiceName { get; set; } = string.Empty;
        /// <summary>
        /// Ngày đăng ký
        /// </summary>
        public DateTime? RegisterAt { get; set; }
        /// <summary>
        /// Khung giờ đăng ký (9:00-10:00, 10:00-11:00,..)
        /// </summary>
        public string RegisterTime { get; set; } = string.Empty;
        /// <summary>
        /// Thời gian khám dự kiến
        /// </summary>
        public DateTime? ExpectedAppointmentAt { get; set; }
        /// <summary>
        /// 1: BHYT, 0: Không BHYT
        /// </summary>
        public int HealthInsurance { get; set; }
        /// <summary>
        /// Số tiền trước giảm giá (number) (VND)
        /// </summary>
        public decimal SubTotalAmount { get; set; }
        /// <summary>
        /// Số tiền được giảm 
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;
        /// <summary>
        /// Giá trị khuyến mãi khi mua dịch vụ
        /// </summary>
        public decimal DiscountValue { get; set; } = 0;
        /// <summary>
        /// Đơn vị của khuyến mãi khi mua dịch vụ (% - PERCENT, số tiền - MONEY,...)
        /// </summary>
        public string? DiscountUnit { get; set; }
        /// <summary>
        /// Số tiền sau giảm giá (VND)
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;
        /// <summary>
        /// Đơn vị tiền tệ: VND, USD,..
        /// </summary>
        public string Currency { get; set; } = "VND";
        /// <summary>
        /// Giá tiền theo đơn vị tiền tệ
        /// </summary>
        public decimal UnitPriceOfCurrency { get; set; }
        /// <summary>
        /// Tỉ lệ chuyển đổi tiền tệ
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;
        /// <summary>
        /// Mã tiếp nhận tích hợp với His
        /// </summary>
        public string RefNo { get; set; } = string.Empty;
        /// <summary>
        /// Code tiếp nhận của HIS (Đà Nẵng)
        /// </summary>
        public string RefDocNo { get; set; } = string.Empty;
        /// <summary>
        /// Tỉ lệ bảo hiểm (phần trăm)
        /// </summary>
        public string RateOfInsurance { get; set; } = string.Empty;
        /// <summary>
        /// Vị trí khám
        /// </summary>
        public string ExaminationLocation { get; set; } = string.Empty;
        /// <summary>
        /// Mã bệnh nhân
        /// </summary>
        public string PatientCode { get; set; } = string.Empty;
        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public string? HospitalId { get; set; } = string.Empty;
        /// <summary>
        /// Số thứ tự
        /// </summary>
        public string QueueNumber { get; set; } = string.Empty;
        /// <summary>
        /// Ưu tiên số thứ tự
        /// </summary>
        public bool QueueNumberPriority { get; set; } = false;
        /// <summary>
        /// Id phòng khám
        /// </summary>
        public string ClinicId { get; set; } = string.Empty;
        /// <summary>
        /// Mã phòng khám
        /// </summary>
        public string ClinicCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên phòng khám
        /// </summary>
        public string Clinic { get; set; } = string.Empty;
        /// <summary>
        /// Id nhóm phòng khám
        /// </summary>
        public string ClinicGroupId { get; set; } = string.Empty;
        /// <summary>
        /// Mã nhóm phòng khám
        /// </summary>
        public string ClinicGroupCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên nhóm phòng khám
        /// </summary>
        public string ClinicGroup { get; set; } = string.Empty;
        /// <summary>
        /// Mã phòng khám con
        /// </summary>
        public string SubClinicId { get; set; } = string.Empty;
        /// <summary>
        /// Tên phòng khám con
        /// </summary>
        public string SubClinic { get; set; } = string.Empty;
        /// <summary>
        /// Mã loại khám (Bảo hiểm, dịch vụ,..)
        /// </summary>
        public string? ExameTypeId { get; set; } = string.Empty;
        /// <summary>
        /// Tên loại khám
        /// </summary>
        public string ExameType { get; set; } = string.Empty;

        public string? LinkCode { get; set; } = string.Empty;

        /// <summary>
        /// Phân tuyến bảo hiểm 1(đúng tuyến), 2(trái tuyến), 3(thông tuyến), 4(chuyển tuyến)
        /// </summary>
        public string ReferralLevel { get; set; } = string.Empty;

        /// <summary>
        /// Số giấy chuyển tuyến
        /// </summary>
        public string TransferReferralDocumentNumber { get; set; } = string.Empty;

        /// <summary>
        /// Mã bệnh chuyển tuyến
        /// </summary>
        public string TransferReferralDiseaseCode { get; set; } = string.Empty;

        /// <summary>
        /// Tên bệnh chuyển tuyến
        /// </summary>
        public string TransferReferralUnit { get; set; } = string.Empty;

        /// <summary>
        /// Code luồng tái khám (Number phiếu đăng ký)
        /// </summary>
        public string? RefRegisterNumber { get; set; } = string.Empty;

        /// <summary>
        /// Tuyến khám bệnh: Đúng tuyến, trái tuyến, Đa tuyến đúng tuyến
        /// </summary>
        public string? HealthcareServiceTierId { get; set; }

        /// <summary>
        /// Loại hình khám bệnh: Khám bệnh, ngoại trú, Nội trú....
        /// </summary>
        public string? HealthcareServiceTypeId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh: 1. Đúng tuyến, 1.1....
        /// </summary>
        public string? MedicalTreatmentCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Tên đối tượng khám chữa bệnh
        /// </summary>
        public string MedicalTreatmentCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// Mã đối tượng khám chữa bệnh HIS: Bảo hiểm, tự nguyện, viện phí,..
        /// </summary>
        public string? MedicalTreatmentCategoryHisId { get; set; }

        /// <summary>
        /// Lý do khám bệnh
        /// </summary>
        public string? ReasonForVisit { get; set; }

        /// <summary>
        /// Trạng thái tạo phiếu qua HIS
        /// </summary>
        public bool IsHisCreateFormSuccess { get; set; } = false;

        /// <summary>
        /// Nội dung message từ HIS trả về
        /// </summary>
        public string? HisMessageWhenCreateForm { get; set; }

        /// <summary>
        /// KioskId phát sinh
        /// </summary>
        public string? DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// Loại phiếu: DANG_KY, DANG_KY_N, GOI_KHAM, DAT_LICH, DANG_KY_THANH_VIEN (luồng Membership)
        /// </summary>
        public string RegisterType { get; set; } = "DANG_KY"; //Mặc định là đăng ký kiosk

        /// <summary>
        /// Id booking
        /// </summary>
        public string PatientBookingNumber { get; set; } = string.Empty;
        //Loại hình thanh toán: Tiền mặt (cash), thẻ (card), chuyển khoản (qr),...truy
        public string? PaymentType { get; set; }
        /// <summary>
        /// Tên bệnh nhân (victoria)
        /// </summary>
        public string PatientNameRef { get; set; } = string.Empty;
        /// <summary>
        /// Tên bệnh nhân (victoria)
        /// </summary>
        public string PatientDateOfBirthRef { get; set; } = string.Empty;
        /// <summary>
        /// Số điện thoại bệnh nhân (victoria)
        /// </summary>
        public string PatientPhoneRef { get; set; } = string.Empty;

        public virtual Customer? Customer { get; set; }

        public virtual Membership? Membership { get; set; }

        public virtual Hospital? Hospital { get; set; }
    }
}