using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Phiếu đăng ký khám chữa bệnh tạm áp dụng cho mobile
    /// </summary>
    public class PatientBooking : BaseAudit
    {
        public string Number { get; set; } = string.Empty;
        public string CustomerKey { get; set; } = string.Empty;
        public string CustomerRelationKey { get; set; } = string.Empty;
        public string HealthServiceId { get; set; } = string.Empty;
        public string ServiceCode { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        public decimal AdvancePayment { get; set; } = decimal.Zero;
        public string MaPhong { get; set; } = string.Empty;
        public int UuTien { get; set; } = 1;
        public string ExameTypeId { get; set; } = string.Empty;
        public string ClinicId { get; set; } = string.Empty;
        public string SubClinicId { get; set; } = string.Empty;
        public string ClinicCode { get; set; } = string.Empty;
        public string ClinicName { get; set; } = string.Empty;
        public string ClinicGroupId { get; set; } = string.Empty;
        public string ClinicGroupCode { get; set; } = string.Empty;
        public bool IsInsurance { get; set; }
        public string? RefRegisterNumber { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }

        /// <summary>
        /// Tuyến khám bệnh: Đúng tuyến, trái tuyến, Đa tuyến đúng tuyến
        /// </summary>
        public string? HealthcareServiceTierId { get; set; }

        /// <summary>
        /// Loại hình khám bệnh: Khám bệnh, ngoại trú, Nội trú....
        /// </summary>
        public string? HealthcareServiceTypeId { get; set; }

        public string? MedicalTreatmentCategoryId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh HIS: Bảo hiểm, tự nguyện, viện phí,..
        /// </summary>
        public string? MedicalTreatmentCategoryHisId { get; set; }

        /// <summary>
        /// Lý do khám bệnh
        /// </summary>
        public string? ReasonForVisit { get; set; }

        public string? EducationLevel { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkAddress { get; set; }

        /// <summary>
        /// Số giấy chuyển tuyến
        /// </summary>
        public string? TransferReferralDocumentNumber { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến
        /// </summary>
        public string? TransferReferralDiseaseCode { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến (Kèm tên bệnh)
        /// </summary>
        public string? TransferReferralDiseaseCodeAndName { get; set; }

        /// <summary>
        /// Đơn vị chuyển tuyến
        /// </summary>
        public string? TransferReferralUnit { get; set; }

        /// <summary>
        /// Hình thức chuyển tuyến
        /// </summary>
        public string? TransferReferralType { get; set; }

        /// <summary>
        /// Lý do chuyển tuyến
        /// </summary>
        public string? TransferReferralReason { get; set; }

        /// <summary>
        /// Ngày chuyển tuyến
        /// </summary>
        public string? TransferReferralDate { get; set; }
        /// <summary>
        /// Thông tin chuẩn đoán tuyến dưới
        /// </summary>
        public string? TransferReferralDiagnosisInfo { get; set; }
        public string? ProvinceId { get; set; }
        public string? WardId { get; set; }
        public string? DistrictId { get; set; }
        public string? Street { get; set; }

        //Chỉ số sinh tồn
        public string? BloodPressure { get; set; } = string.Empty;
        public int? HeartRate { get; set; }
        public int? RespiratoryRate { get; set; }
        public double? BloodOxygen { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public int? PulseRate { get; set; }
        public double? Temperature { get; set; }

        //Loại hình thanh toán: Tiền mặt (cash), thẻ (card), chuyển khoản (qr),...truy
        public string? PaymentType { get; set; }

        //Có điều trị arv không
        public bool IsOnARVTreatment { get; set; }
        public string? PatientId { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;

        /// <summary>
        /// Id bệnh nhân
        /// </summary>
        public string? CustomerId { get; set; } = string.Empty;
        public virtual Customer? Customer { get; set; }
        public virtual Hospital? Hospital { get; set; }


        #region Thông tin lịch hẹn
        /// <summary>
        /// Ngày hẹn khám
        /// </summary>
        public DateTime? AppointmentDate { get; set; }
        /// <summary>
        /// Khoảng thời gian đặt hẹn khám. Format: HH:mm - HH:mm
        /// </summary>
        public string? AppointmentTimeZone { get; set; }
        #endregion
        #region Thông tin checkin
        /// <summary>
        /// PENDING: đã tạo, chờ checkin
        /// SUCCESS: đã checkin tạo đơn mới
        /// CANCEL: đã hủy
        /// </summary>
        public string? Status { get; set; } = PatientBookingStatus.PENDING;
        public DateTime? RegisterAt { get; set; }
        #endregion Thông tin checkin
    }

    public static class PatientBookingStatus
    {
        /// <summary>
        /// đã tạo, chờ checkin
        /// </summary>
        public const string PENDING = nameof(PENDING);
        /// <summary>
        /// đã checkin tạo đơn mới
        /// </summary>
        public const string SUCCESS = nameof(SUCCESS);
        /// <summary>
        /// đã hủy
        /// </summary>
        public const string CANCEL = nameof(CANCEL);
        /// <summary>
        /// Hết hạn
        /// </summary>
        public const string EXPIRED = nameof(EXPIRED);
    }
}