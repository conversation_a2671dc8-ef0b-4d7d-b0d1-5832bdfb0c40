using MediTrack.Domain.Bases;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin chung của bệnh viện
    /// </summary>
    public class HospitalMetaData : BaseAudit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Title { get; set; }
        public string? Code { get; set; }
        public string? Value { get; set; }
        public string HospitalId { get; set; } = string.Empty;
    }
}
