using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class EquipmentRevoke : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        // kho
        public string? WarehousePartnerId { get; set; } = string.Empty;
        public WarehousePartner? WarehousePartner { get; set; }

        // Trạng thái
        public string Status { get; set; } = string.Empty;
        // Người thao tác
        public string? RevokeAccount { get; set; } = string.Empty;
        public string? RevokeAddress { get; set; } = string.Empty;
        public string? RevokePhone { get; set; } = string.Empty;
        public DateTime? RevokeAt { get; set; }

        // Ghi chú
        public string? Note { get; set; } = string.Empty;
        public string? Phone { get; set; } = string.Empty;

        public List<EquipmentRevokeDetail>? EquipmentRevokeDetails { get; set; }
        public List<EquipmentRevokeHistory>? EquipmentRevokeHistories { get; set; }

        public string? HopistalId { get; set; }
        public Hospital? Hospital { get; set; }
        public string? HdBankBranchId { get; set; }
        public string? HdBankBranchName { get; set; }
    }
}
