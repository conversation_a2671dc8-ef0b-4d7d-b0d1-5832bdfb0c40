﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ExameType : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsInsurance { get; set; }
        //không tự động check bảo hiểm (cho c<PERSON>c viện tự động check bảo hiểm)
        public bool IsIgnoreAutoCheckInsurance { get; set; }
        public bool IsDisabled { get; set; }
        /// <summary>
        /// Thứ tự sắp xếp
        /// </summary>
        public int SortIndex { get; set; }
        public string? HospitalId { get; set; }
    }
}
