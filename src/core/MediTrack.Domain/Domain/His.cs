﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON><PERSON> sách his
    /// </summary>
    public class His : BaseAudit
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Tên của HIS
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Version của HIS
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// Tình trạng triển khai của HIS
        /// </summary>
        public string DeploymentStatus { get; set; } = string.Empty;

        public virtual List<Hospital>? Hospitals { get; set; }
        public virtual List<HisMetaData>? HisMetaDatas { get; set; }
    }
}
