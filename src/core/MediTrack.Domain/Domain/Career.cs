﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin nghề nghiệp
    /// </summary>
    public class Career : BaseAudit
    {
        /// <summary>
        /// Mã nghề nghiệp (4750)
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Tên nghề nghiệp
        /// </summary>
        public string Name { get; set; } = string.Empty;
        /// <summary>
        /// Mã nghề nghiệp phía ngân hàng
        /// </summary>
        public string? BankCareerCodeRef { get; set; }
        /// <summary>
        /// Cấp độ nghề nghiệp (phân cấp cho Select)
        /// </summary>
        public int Level { get; set; } = 0;
        /// <summary>
        /// Mã nghề nghiệp cha
        /// </summary>
        public string? ParentId { get; set; }
        /// <summary>
        /// Thứ tự sắp xếp
        /// </summary>
        public int Order { get; set; } = 9999;
        /// <summary>
        /// Mặc định
        /// </summary>
        public bool IsDefault { get; set; } = false;
    }
}
