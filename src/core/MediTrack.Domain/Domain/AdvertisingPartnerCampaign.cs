using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Bảng liên kết giữa đối tác quảng cáo và chiến dịch (quan hệ nhiều-nhiều)
    /// </summary>
    public class AdvertisingPartnerCampaign : BaseAudit
    {
        /// <summary>
        /// Định danh duy nhất cho bản ghi đối tác trong chiến dịch
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// ID của đối tác quảng cáo
        /// </summary>
        public string PartnerId { get; set; } = string.Empty;
        
        /// <summary>
        /// Tham chiếu đến đối tác quảng cáo
        /// </summary>
        public virtual AdvertisingPartner Partner { get; set; } = null!;
        
        /// <summary>
        /// ID của chiến dịch quảng cáo
        /// </summary>
        public string CampaignId { get; set; } = string.Empty;
        
        /// <summary>
        /// Tham chiếu đến chiến dịch quảng cáo
        /// </summary>
        public virtual AdvertisingCampaign Campaign { get; set; } = null!;
        
        /// <summary>
        /// Thứ tự hiển thị của đối tác trong chiến dịch
        /// </summary>
        public int DisplayOrder { get; set; }
        
        /// <summary>
        /// Thứ tự hiển thị của media trong danh sách media của đối tác
        /// </summary>
        public int MediaOrder { get; set; }
        
        /// <summary>
        /// Loại media (Image, Video, GIF)
        /// </summary>
        public string MediaType { get; set; } = string.Empty;
        
        /// <summary>
        /// Đường dẫn đến file media
        /// </summary>
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// Tên gốc của file
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// Định dạng file (PNG, JPG, MP4...)
        /// </summary>
        public string FileFormat { get; set; } = string.Empty;
        
        /// <summary>
        /// Kích thước file
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// Chiều rộng của media
        /// </summary>
        public int Width { get; set; }
        
        /// <summary>
        /// Chiều cao của media
        /// </summary>
        public int Height { get; set; }
        
        /// <summary>
        /// Thời gian hiển thị tính bằng giây
        /// </summary>
        public int DurationInSeconds { get; set; }
        
        /// <summary>
        /// Media này có đang hoạt động hay không
        /// </summary>
        public bool IsMediaActive { get; set; } = true;
    }
}