using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class AppBanner : BaseAudit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? Title { get; set; }
        public string? Url { get; set; }
        public float? Latitude { get; set; }
        public float? Longitude { get; set; }
        public string? Type { get; set; } = string.Empty;
        public bool? IsDefault { get; set; }
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Province { get; set; }
    }
}