using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class KioskMetaData : BaseAudit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Code { get; set; }
        public string? Title { get; set; }
        public string? Value { get; set; }
        public string? HospitalId { get; set; }
        public virtual Hospital? Hospital { get; set; }
        public string? KioskId { get; set; }
        public virtual Kiosk? Kiosk { get; set; }
    }
}