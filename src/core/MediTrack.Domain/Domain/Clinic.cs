﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Danh sách khoa, phòng khám
    /// </summary>
    public class Clinic : BaseAudit
    {
        /// <summary>
        /// Id khoa, phòng khám
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Mã khoa, phòng khám
        /// </summary>
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// Tên khoa, phòng khám
        /// </summary>
        public string? Name { get; set; } = string.Empty;
        /// <summary>
        /// Id loại khám
        /// </summary>
        public string? ExameTypeID { get; set; } = string.Empty;
        /// <summary>
        /// Số chờ khám
        /// </summary>
        public int? WaitingPatientCount { get; set; }
        /// <summary>
        /// Số lượng bệnh nhân khám còn lại
        /// </summary>
        public int? RemainingPatientCount { get; set; }
        /// <summary>
        /// Số lượng bệnh nhân tổng cộng
        /// </summary>
        public int? TotalPatientCount { get; set; }
        /// <summary>
        /// STT bệnh nhân đang khám
        /// </summary>
        public int? ProcessingNumber { get; set; }

        /// <summary>
        /// Thứ tự sắp xếp
        /// </summary>
        public int SortIndex { get; set; }

        [NotMapped]
        public List<Clinic> Children { get; set; } = [];

        public string? HospitalId { get; set; }
        public virtual Hospital? Hospital { get; set; }
    }
}
