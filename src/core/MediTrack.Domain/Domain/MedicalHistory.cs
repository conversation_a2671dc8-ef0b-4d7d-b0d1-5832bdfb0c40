﻿namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON>h sách dịch vụ khám, chữ<PERSON> bệnh theo khoa
    /// </summary>
    public class MedicalHistory
    {
        /// <summary>
        /// Id lịch sử KCB
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Id dịch vụ
        /// </summary>
        public string HealthServiceId { get; set; } = string.Empty;
        /// <summary>
        /// Code dịch vụ
        /// </summary>
        public string HealthServiceCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên dịch vụ
        /// </summary>
        public string HealthServiceName { get; set; } = string.Empty;
        /// <summary>
        /// Id chuyên khoa
        /// </summary>
        public string? ClinicId { get; set; }
        /// <summary>
        /// Mã chuyên khoa (BV Đức Giang có mã chuyên khoa)
        /// </summary>
        public string? ClinicCode { get; set; }
        /// <summary>
        /// Id nhóm phòng khám
        /// </summary>
        public string? ClinicGroupId { get; set; }
        /// <summary>
        /// Tên nhóm phòng khám
        /// </summary>
        public string? ClinicGroup { get; set; }
        /// <summary>
        /// Chuyên khoa
        /// </summary>
        public string? Clinic { get; set; }
        /// <summary>
        /// Loại dịch vụ khám
        /// </summary>
        public string? ExameTypeId { get; set; }
        /// <summary>
        /// Loại dịch vụ khám
        /// </summary>
        public string? ExameType { get; set; }
        /// <summary>
        /// Thời gian khám bệnh
        /// </summary>
        public string? ExaminationHour { get; set; }
        /// <summary>
        /// Có khám bảo hiểm không
        /// </summary>
        public bool IsInsurance { get; set; }
        /// <summary>
        /// Có ưu tiên hay không
        /// </summary>
        public int? Priority { get; set; }
    }
}
