using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class WarehouseEquipment : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string? EquipmentCatalogId { get; set; }
        public EquipmentCatalog? EquipmentCatalog { get; set; }
        public int? Quantity { get; set; }
        public string? Serial { get; set; }

        public string? ImportedEquipmentId { get; set; }
        public ImportedEquipment? ImportedEquipment { get; set; }

        public string? ExportedEquipmentId { get; set; }
        public ExportedEquipment? ExportedEquipment { get; set; }

        public string? EquipmentRevokeId { get; set; }
        public EquipmentRevoke? EquipmentRevoke { get; set; }

        public string? EquipmentWarrantyId { get; set; }
        public EquipmentWarranty? EquipmentWarranty { get; set; }
    }
}
