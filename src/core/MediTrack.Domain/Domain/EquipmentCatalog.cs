using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class EquipmentCatalog : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? EquipmentTypeCode { get; set; }
        public string? EquipmentTypeName { get; set; }
        public string? EquipmentVendorId { get; set; }
        public EquipmentVendor? EquipmentVendor { get; set; }
        public string ReferenceCode { get; set; } = string.Empty;
        public bool IsActive { get; set; } = false;
        public decimal? Price { get; set; }
        public string? Description { get; set; }

        public string? EquipmentModelId { get; set; }
        public EquipmentModel? EquipmentModel { get; set; }
    }
}
