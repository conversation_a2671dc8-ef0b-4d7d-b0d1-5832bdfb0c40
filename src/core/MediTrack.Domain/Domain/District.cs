﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class District : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? OtherName { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
        public string? ProvinceId { get; set; }
        public virtual Province? Province { get; set; }
        public virtual List<Ward>? Wards { get; set; } = new();
    }
}
