﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class Kiosk : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? HospitalId { get; set; } = string.Empty;
        public string? SecretKey { get; set; } = string.Empty;
        public bool? IsGenQueueNumber { get; set; } = false;
        public bool? IsPaymentDocument { get; set; } = false;
        public bool? IsRegisterDocument { get; set; } = false;
        public bool? IsSupportInsurance { get; set; } = false;
        public bool? IsRePrintRegisterDocument { get; set; } = false;
        public bool? IsSupportInsuranceInServiceScreen { get; set; } = false;

        /// <summary>
        /// Có luồng tái khám không
        /// </summary>
        public bool? IsReExamination { get; set; } = false;

        /// <summary>
        /// <PERSON><PERSON> luồng tái khám theo chỉ định của bác sĩ không
        /// </summary>
        public bool? IsReExaminationByDoctor { get; set; } = false;

        /// <summary>
        /// Có đăng kí khám cho bản thân không
        /// </summary>
        public bool? IsRegisterSelf { get; set; } = true;

        /// <summary>
        /// Có đăng kí khám cho người thân không
        /// </summary>
        public bool? IsRegisterRelative { get; set; } = false;

        /// <summary>
        /// Có hỗ trợ đăng kí khám bằng VNEID không
        /// </summary>
        public bool? IsSupportVNeID { get; set; } = false;

        /// <summary>
        /// Có khám cận lâm sàng không
        /// </summary>
        public bool? IsParaclinicalExamination { get; set; } = false;

        /// <summary>
        /// Id của UltraView
        /// </summary>
        public string? UltraViewId { get; set; } = string.Empty;
        /// <summary>
        /// Mật khẩu của UltraView
        /// </summary>
        public string? UltraViewPassword { get; set; } = string.Empty;
        /// <summary>
        /// Kiosk có đang ở chế độ bảo trì không
        /// </summary>
        public bool IsMaintenanceMode { get; set; } = false;
        /// <summary>
        /// Môi trường: DEV, PROD, TEST
        /// </summary>
        public string Environment { get; set; } = string.Empty;
        /// <summary>
        /// Số serial của thiết bị
        /// </summary>
        public string? Serial { get; set; } = string.Empty;
        /// <summary>
        /// Cờ cập nhật tự động
        /// </summary>
        public bool IsAutoUpdate { get; set; } = false;
        /// <summary>
        /// Cờ tự động UltraViewer
        /// </summary>
        public bool IsAutoOpenUltraViewer { get; set; } = false;
        /// <summary>
        /// Cờ xác định HIS có đi luồng tạm ứng hay không (luồng Dịch vụ)
        /// </summary>
        public bool? IsAdvancePayment { get; set; } = false;
        /// <summary>
        /// Cờ xác định HIS có đi luồng tạm ứng hay không (luồng Bảo hiểm)
        /// </summary>
        public bool? IsInsuranceAdvancePayment { get; set; } = false;
        /// <summary>
        /// Cờ xác định có đang active hay không
        /// </summary>
        public bool IsActive { get; set; } = true;
        /// <summary>
        /// Ngày bắt đầu hiệu lực
        /// </summary>
        public DateTime? EffectiveDate { get; set; }
        /// <summary>
        /// Ngày kết thúc hiệu lực
        /// </summary>
        public DateTime? ExpirationDate { get; set; }
        /// <summary>
        /// Mã checksum của kiosk
        /// </summary>
        public string Checksum { get; set; } = string.Empty;

        /// <summary>
        /// Thông báo của bệnh viện
        /// </summary>
        public string Announcement { get; set; } = string.Empty;

        /// <summary>
        /// Cờ thể hiện kiosk có đang tham gia chiến dịch quảng cáo nào không
        /// </summary>
        public bool IsParticipatingInCampaign { get; set; } = false;

        /// <summary>
        /// Có đăng kí khám luồng đăng kí ở app, check in ở kiosk không (là cờ mặc định của kiosk)
        /// </summary>
        public bool? IsAppRegisterCheckInKiosk { get; set; } = false;

        /// <summary>
        /// Cờ ẩn thanh toán QRCode
        /// </summary>
        public bool? IsHideQrCodePayment { get; set; } = false;

        /// <summary>
        /// Cờ xác định có chức năng đăng kí khám sức khỏe không
        /// </summary>
        public bool? IsHealthCheckRegister { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép chọn nhiều dịch vụ khi đăng kí khám hay không
        /// </summary>
        public bool? IsAllowSelectMultipleServices { get; set; } = false;


        /// <summary>
        /// Cờ xác định bệnh viện này phép in phiếu thủ công hay không
        /// </summary>
        public bool? IsAllowPrintManual { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép cho xem bản đồ hay không
        /// </summary>
        public bool? IsAllowShowHospitalMap { get; set; }

        /// <summary>
        /// Cờ xác định bệnh viện này có cho phép tra cứu dịch vụ hay không
        /// </summary>
        public bool? IsAllowSearchHealthService { get; set; } = false;

        /// <summary>
        /// Danh sách các chiến dịch quảng cáo mà kiosk tham gia
        /// </summary>
        public virtual ICollection<AdvertisingKioskCampaign> CampaignParticipations { get; set; } = [];
    }
}
