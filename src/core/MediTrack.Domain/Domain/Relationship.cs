﻿using MediTrack.Domain.Bases;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Domain
{
    public class Relationship : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ReferId { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
    }
}
