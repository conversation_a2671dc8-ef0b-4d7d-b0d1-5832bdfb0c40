using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ExportedEquipment : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        // kho
        public string? WarehousePartnerId { get; set; }
        public WarehousePartner? WarehousePartner { get; set; }

        // Trạng thái
        public string? Status { get; set; } = string.Empty;

        // Người nhận
        public string? ReceiverName { get; set; } = string.Empty;
        public string? ReceiverPhone { get; set; } = string.Empty;
        public string? ReceiverAddress { get; set; } = string.Empty;
        // Người thao tác
        public string? ExportedAccount { get; set; } = string.Empty;
        public DateTime? ExportedAt { get; set; }

        // Ghi chú
        public string? Note { get; set; } = string.Empty;

        public List<ExportedEquipmentDetail>? ExportedEquipmentDetails { get; set; }

        public string? HopistalId { get; set; }
        public Hospital? Hospital { get; set; }
        public string? HdBankBranchId { get; set; }
        public string? HdBankBranchName { get; set; }
        public List<ExportedEquipmentHistory>? ExportedEquipmentHistories { get; set; }
    }
}
