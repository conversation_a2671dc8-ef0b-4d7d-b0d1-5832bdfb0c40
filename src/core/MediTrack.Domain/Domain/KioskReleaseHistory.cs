﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class KioskReleaseHistory : BaseAudit
    {
        /// <summary>
        /// Version của release. VD: 1.0.0
        /// </summary>
        public string Id { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string PasswordExtract { get; set; } = string.Empty;
    }
}
