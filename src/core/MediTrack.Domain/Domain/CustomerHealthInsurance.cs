﻿using System.ComponentModel.DataAnnotations.Schema;

namespace MediTrack.Domain.Domain
{
    public class CustomerHealthInsurance
    {
        public string HealthInsuranceId { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string IdentityNo { get; set; } = string.Empty;
        public string InsuranceNo { get; set; } = string.Empty;
        public string RegisterPlaceID { get; set; } = string.Empty;
        public string RegisterPlaceName { get; set; } = string.Empty;
        public string FromDate { get; set; } = string.Empty;
        public string ExpiredDate { get; set; } = string.Empty;
        public string FullFiveYearDate { get; set; } = string.Empty;
        public string InsuranceGroupID { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        /// <summary>
        /// Phân tuyến dành riêng cho ehis -1(không auto), 0(đúng tuyến), 1(trái tuyến), 2(thông tuyến), 3(cấp cứu)
        public string RoutingType { get; set; } = "-1";
        /// <summary>
        /// Phân tuyến 1(đúng tuyến), 2(tr<PERSON><PERSON> tuyến), 3(thông tuyến), 4(chuy<PERSON>n tuyến)
        /// </summary>
        public string ReferralLevel { get; set; } = "1";
        /// <summary>
        /// Có cho phép khám bệnh hay không (Đúng thì mới cho phép khám bệnh)
        /// </summary>
        public bool IsCorrectRouting { get; set; } = false;
        /// <summary>
        /// Đia chỉ đăng ký bảo hiểm
        /// </summary>
        public string RegisterAddress { get; set; } = string.Empty;

        /// <summary>
        /// Tin nhắn từ HIS
        /// </summary>
        public string HisMessage { get; set; } = string.Empty;

        /// <summary>
        /// Mã KV
        /// </summary>
        public string AreaCode { get; set; } = string.Empty;

        [NotMapped]
        public List<MedicalHistoryModel> MedicalHistories { get; set; } = [];

        [NotMapped]
        public List<CheckInsuranceHistoryModel> CheckInsuranceHistories { get; set; } = [];
        
        [NotMapped]
        public InsuranceDataDefaultModel? InsuranceDataDefaultModel { get; set; } = null;
    }

    public class MedicalHistoryModel
    {
        public string RecordId { get; set; } = string.Empty;
        public string HealthcareFacilityId { get; set; } = string.Empty;
        public string AdmissionDate { get; set; } = string.Empty;
        public string DischargeDate { get; set; } = string.Empty;
        public string DiseaseName { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string TreatmentResult { get; set; } = string.Empty;
        public string AdmissionReason { get; set; } = string.Empty;
    }

    public class CheckInsuranceHistoryModel
    {
        public string CheckUser { get; set; } = string.Empty;
        public string CheckTime { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
    }

    public class InsuranceDataDefaultModel
    {
        public string? HealthcareServiceTierId { get; set; }
        public string? MedicalTreatmentCategoryId { get; set; }
    }   
}
