﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class ObjectPermission : BaseAudit
    {
        public Guid Id { get; set; }
        public string? UserId { get; set; }
        public string? RoleId { get; set; }
        public string? ObjectName { get; set; } = string.Empty;
        public string? Criteria { get; set; } = string.Empty;
        public bool CanCreate { get; set; }
        public bool CanDelete { get; set; }
        public bool CanUpdate { get; set; }
        public bool CanRead { get; set; }
    }
}
