﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON>a dữ li<PERSON>u củ<PERSON> d<PERSON><PERSON> v<PERSON>, ch<PERSON><PERSON> b<PERSON><PERSON>
    /// </summary>
    public class HealthServiceMetaData : BaseAudit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Code { get; set; }
        public string? Title { get; set; }
        public string? Value { get; set; }
        public string HealthServiceId { get; set; } = string.Empty;
    }
}
