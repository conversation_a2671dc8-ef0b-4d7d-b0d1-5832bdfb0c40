﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Constants
{
    public class ErrorConstant
    {
        public static readonly string DUPLICATE_EMAIL = "01";
        public static readonly string ACCOUNT_EXISTED = "02";
        public static readonly string ACCOUNT_NOT_ACTIVE = "03";
        public static readonly string ACCOUNT_OR_PASSWORD_INVALID = "04";

        public static readonly string ACCOUNT_LOCKED = "05";
        public static readonly string MISSING_REQUIRE_DATA = "06";
        public static readonly string INVALID_DATA = "07";
        public static readonly string NOT_FOUND_DATA = "08";
        public static readonly string EXPIRED_DATA = "09";
        public static readonly string DUPLICATE_KEY = "10";

        public static readonly string EMAIL_EXIST = "11";
        public static readonly string PHONE_EXIST = "12";

        public static readonly string HOSPITAL_EXIT = "13";
        public static readonly string CONFIG_NOT_FOUND = "14";
        public static readonly string PROJECT_NOT_WATING_APPROVE = "17";
        public static readonly string PROJECT_NOT_PUBLISHED = "18";
        public static readonly string PROJECT_NOT_PASSED = "19";
        public static readonly string SOCIAL_HAS_BEEN_LINKED = "20";
        public static readonly string INVALID_ADDRESS = "21";

        public static readonly string CREATE_FAIL = "95";
        public static readonly string UPDATE_FAIL = "96";
        public static readonly string DELETE_FAIL = "97";
        public static readonly string TOO_MANY_REQUEST = "98";
        public static readonly string INVALID_CLIENT = "97";
        public static readonly string UNKNOWN = "99";

        public static readonly string SESSION_HAS_BEEN_USED = "34";

        public static readonly string SUCCESS = "00";
        public static readonly string PERMISSION_DENIED = "87";
        public static readonly string SHOP_NOT_FOUND = "88";
        public static readonly string PAYMENT_TRANSACTION_NOT_FOUND = "89";
        public static readonly string ORDER_NOT_FOUND = "94";
        public static readonly string MERCHANT_NOT_FOUND = "93";
        public static readonly string ERROR_INTEGRATION = "92";
        public static readonly string VALIDATION = "91";
        public static readonly string INVOICE_NOT_FOUND = "90";
        public static readonly string SIGNATURE_FAILED = "06";
        public static readonly string INVALID_TRANSACTION_AMOUNT = "40";
        public static readonly string INVALID_SIGN = "98";
        public static readonly string UNKNOW = "99";

        public static string ConvertDumy(string input)
        {
            string code = input switch
            {
                "OK" => "00",
                "NotFound" => "08",
                "Created" => "00",
                "Updated" => "00",
                "Deleted" => "00",
                "Invalid address" => "21",
                "CreatedError" => "95",
                "UpdatedError" => "96",
                "DeletedError" => "97",
                "Auth successfully" => "00",
                "Invalid username" => "04",
                "Password incorrect" => "04",
                _ => "99"
            };

            return code;
        }
    }
}
