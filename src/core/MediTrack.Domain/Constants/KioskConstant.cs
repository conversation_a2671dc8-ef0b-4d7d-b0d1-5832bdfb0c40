﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Constants
{
    public class KioskConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "Kiosks";
        public const string BadRequest = nameof(BadRequest);
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";
    }
}
