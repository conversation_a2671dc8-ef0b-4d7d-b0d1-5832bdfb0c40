﻿namespace MediTrack.Domain.Constants
{
    public abstract class ReceiptConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "Receipts";
        public const string BadRequest = nameof(BadRequest);
        public const string InvalidReceipt = "Invalid receipt number!";
        public const string InvalidAmount = "Invalid receipt amount!";
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";

        public const string InvalidSignature = "Invalid signature on update payment status";

        public const string STATUS_NEW = "NEW";
        public const string STATUS_PAID = "PAID";

        public const string CREATED_FROM_PARTNER = "CREATED_FROM_PARTNER";
    }
}
