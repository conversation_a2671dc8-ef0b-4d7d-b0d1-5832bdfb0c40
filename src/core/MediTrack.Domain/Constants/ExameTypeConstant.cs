﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MediTrack.Domain.Domain;

namespace MediTrack.Domain.Constants
{
    public abstract class ExameTypeConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "ExameTypes";
        public const string BadRequest = nameof(BadRequest);
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";

        public static List<ExameType> ExameTypes =>
            [
                new ExameType
                {
                    Id = "",
                    Name = "Khám dịch vụ",
                    IsInsurance = false
                },
                new ExameType
                {
                    Id = "",
                    Name = "Khám bảo hiểm",
                    IsInsurance = true
                }
            ];
    }
}
