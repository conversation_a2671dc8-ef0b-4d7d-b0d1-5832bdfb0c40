﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Constants
{
    public abstract class UserConstant
    {
        public const string Ok = nameof(Ok);
        public const string DefaultPassword = "P@ssW0rd@123#@!";
        public const string NotFound = nameof(NotFound);
        public const string BadRequest = nameof(BadRequest);
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";
        public const string RequiredUserNameMessage = "Username can not be null or empty";
        public const string RequiredFullNameMessage = "Fullname can not be null or empty";
        public const string RequiredPasswordMessage = "Password can not be null or empty";
        public const string AuthSuccess = "Auth successfully";
        public const string InvalidUserName = "Invalid username";
        public const string InvalidPassword = "Password incorrect";
        public const string ChangePasswordFail = "Change password fail";
        public const string ChangePasswordSuccess = "Change password Successfully";
        public const string TableName = "Users";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";
    }
}
