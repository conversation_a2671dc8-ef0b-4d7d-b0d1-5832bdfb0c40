﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Constants
{
    public class PaymentConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "Payments";
        public const string BadRequest = nameof(BadRequest);
        public const string DefaultType = "QR";
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";

        public const string InvalidPayment = "Invalid Payment";
        public const string InvalidMerchantId = "Invalid Merchant";
        public const string NotFoundMerchant = "Not Found Merchant";
        public const string UpdateIpnFail = "Update Ipn Fail";

        public const string NewPayment = "NEW";
        public const string WaitForPayment = "WAIT";
        public const string FailPayment = "FAIL";
        public const string Success = "SUCCESS";
        public const string Partial = "PARTIAL";
        public const string Expired = "EXPIRED";

        public static string StatusSwitchCode(string code) => code switch
        {
            "NEW" => "01",
            "SUCCESS" => "00",
            "PARTIAL" => "10",
            "PAID" => "00", 
            _ => "99"
        };
    }
}