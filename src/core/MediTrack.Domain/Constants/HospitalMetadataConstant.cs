﻿namespace MediTrack.Domain.Constants
{
    public class HospitalMetadataConstant
    {
        public const string PrinterReceipt = "PRINTER_RECEIPT"; // in phiếu đăng kí
        public const string PrinterReceiptInsurance = "PRINTER_RECEIPT_INSURANCE"; // in phiếu đăng kí bảo hiểm

        public const string PrinterReceiptHealthPackage = "PRINTER_RECEIPT_HEALTH_PACKAGE"; // in phiếu đăng kí gói khám sức khỏe
        public const string PrinterReceiptHealthServiceN = "PRINTER_RECEIPT_HEALTH_SERVICE_N"; // in phiếu đăng kí nhiều dịch vụ khám chữa bệnh
        
        public const string PrinterPayment = "PRINTER_PAYMENT"; // in phiếu thu

        public const string PrinterPaymentInsurance = "PRINTER_PAYMENT_INSURANCE"; // in phiếu thu bảo hiểm
        
        public const string PrinterPaymentHealthPackage = "PRINTER_PAYMENT_HEALTH_PACKAGE"; // in phiếu thu gói khám sức khỏe
        public const string PrinterPaymentHealthServiceN = "PRINTER_PAYMENT_HEALTH_SERVICE_N"; // in phiếu thu nhiều dịch vụ khám chữa bệnh

        public const string PrinterQueue = "PRINTER_QUEUE"; // in số thứ tự

        public const string PrinterStatusPayment = "PRINTER_PAYMENT_STATUS_VALUE"; // in phiếu thu
        public const string PrinterStatusReceipt = "PRINTER_RECEIPT_STATUS_VALUE"; // in phiếu thu

        public const string PrinterStatusPaymentByAmount = "PRINTER_PAYMENT_STATUS_VALUE_BY_AMOUNT"; // in phiếu thu

        public const string Paraclinical = "PARACLINICAL"; // in phiếu cận lâm sàng
        public const string ParaclinicalIndications = "PARACLINICAL_INDICATIONS"; // mẫu chứa cặp json để in phiếu cận lâm sàng
        public const string ParaclinicalService = "START_SERVICE_TEMPLATE"; // mẫu chứa cặp json hiển thị dịch vụ để in phiếu cận lâm sàng
    }
}