﻿namespace MediTrack.Domain.Constants
{
    public class CustomerReviewConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "CustomerReviews";
        public const string BadRequest = nameof(BadRequest);
        public const string InvalidReviewType = "Invalid Review Type";
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";

        public const string RegisterFlag = "REGISTER";
        public const string ReExaminationFlag = "REEXAMINATION";
        public const string RegisterRelativeFlag = "REGISTER_RELATIVE";
        public const string QueueNumberFlag = "QUEUE_NUMBER";
        public const string PaymentFlag = "PAYMENT";
        public const string OtherFlag = "OTHER";

        public const string RegisterFlagName = "Đăng ký khám";
        public const string ReExaminationFlagName = "Tái khám";
        public const string RegisterRelativeFlagName = "Đăng ký cho người thân";
        public const string QueueNumberFlagName = "Lấy số thứ tự";
        public const string PaymentFlagName = "Thanh toán";
        public const string OtherFlagName = "Dịch vụ khác";

        public const string ReviewTypeBad = "BAD";
    }
}
