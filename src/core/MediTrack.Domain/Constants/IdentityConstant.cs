﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Constants
{
    public abstract class IdentityConstant
    {
        public const string Ok = nameof(Ok);
        public const string NotFound = nameof(NotFound);
        public const string Forbidden = nameof(Forbidden);
        public const string TableName = "Identity";
        public const string BadRequest = nameof(BadRequest);
        public const string SaveChangesError = "Save Changes Has Errors";
        public const string SaveChangesSuccess = "Save Changes Successfully";
        public const string InvalidIdentity = "Invalid Identity";
        public const string CanNotRead = "You don't have permission to read";
        public const string CanNotCreate = "You don't have permission to create";
        public const string CanNotUpdate = "You don't have permission to update";
        public const string CanNotDelete = "You don't have permission to delete";

        public const string CreateSuccessFully = "Created";
        public const string UpdateSuccessFully = "Updated";
        public const string DeleteSuccessFully = "Deleted";
        public const string CreateError = "CreatedError";
        public const string UpdateError = "UpdatedError";
        public const string DeleteError = "DeletedError";
    }
}
