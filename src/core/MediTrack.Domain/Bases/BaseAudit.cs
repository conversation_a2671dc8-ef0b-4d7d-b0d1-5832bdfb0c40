﻿namespace MediTrack.Domain.Bases
{
    public class BaseAudit
    {
        public string? CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; } = string.Empty;
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public bool IsDeleted { get; set; } = false;

        public void SetCreate(string user)
        {
            CreatedBy = user;
            CreatedAt = DateTime.Now;
        }

        public void SetUpdate(string user)
        {
            UpdatedBy = user;
            UpdatedAt = DateTime.Now;
        }
    }
}
