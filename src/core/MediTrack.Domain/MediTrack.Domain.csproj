﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>1.0.13</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Levenshtein" Version="1.0.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MediTrack.Ultils\MediTrack.Ultils.csproj" />
  </ItemGroup>

</Project>
