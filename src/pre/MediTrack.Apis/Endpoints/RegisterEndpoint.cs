using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.RegisterLogic.Commands;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Queries;
using MediTrack.Domain.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class RegisterEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/register-forms")
               .WithTags("RegisterForms")
               .WithOpenApi()
               .RequireRateLimiting("fixed")
               .MapPost(CreateRegisterForm)
               .MapPost(CreateRegisterFormWithMultiService, "multi-service")
               .MapPost(CreateRegisterFormFromBooking, "booking")
               .MapPost(CreateRegisterFormForHealthPackage, "health-package")
               .MapGet(GetHealthServiceByNumber, "{number}/health-service")
               .MapGet(GetDetail, "detail")
               .MapGet(GetRegisterDocument, "{number}/register-document")
               .MapGet(GetHistoryRegister, "{customerId}/history-register-document")
               .MapGet(GetRegisterDocumentJson, "{number}/register-document-json");
        }

        [SwaggerOperation(Summary = "Api tạo phiếu tiếp nhận",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<CreateRegisterFormResponseDto>>> CreateRegisterForm(
            [FromBody] CreateRegisterForm req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo phiếu tiếp nhận từ phiếu hẹn",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<CreateRegisterFormFromBookingResponseDto>>> CreateRegisterFormFromBooking(string Number, [FromBody] CreateRegisterFormFromBooking req, ISender mediator)
        {
            req.Number = Number;
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo phiếu tiếp nhận cho gói khám",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<CreateRegisterFormForHealthPackageResponseDto>>> CreateRegisterFormForHealthPackage(
            [FromBody] CreateRegisterFormForHealthPackage req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo phiếu tiếp nhận với nhiều dịch vụ",
        Description = "Tạo phiếu tiếp nhận với nhiều dịch vụ và lưu thông tin chi tiết đăng ký")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<CreateRegisterFormWithMultiServiceResponseDto>>> CreateRegisterFormWithMultiService(
            [FromBody] CreateRegisterFormWithMultiService req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data, result.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy chứng từ phiếu tiếp nhận (Không dùng)",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<RegisterDocumentDto>>> GetRegisterDocument(
            string number, ISender mediator)
        {
            var req = new GetRegisterDocument()
            {
                Number = number,
            };

            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy chứng từ phiếu tiếp nhận (Không dùng)",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<PatientBookingDto>>> GetDetail(
            [AsParameters] GetBookingDetail req, ISender mediator)
        {
            var result = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy thông tin KCB của khách hàng",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<HistoryRegisterDto>>>> GetHistoryRegister(
          string customerId, ISender mediator)
        {
            var req = new GetHistoryRegister()
            {
                CustomerId = customerId,
            };

            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy ra thông tin dịch vụ khám chữa bệnh số phiếu")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<HealthService>>> GetHealthServiceByNumber(string number, ISender mediator)
        {
            var req = new GetHealthServiceByNumber()
            {
                Number = number,
            };

            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Api lấy chứng từ phiếu thu để in (json)",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<string>>> GetRegisterDocumentJson(
            string number, ISender mediator)
        {
            var req = new GetRegisterJson() { Number = number };
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}