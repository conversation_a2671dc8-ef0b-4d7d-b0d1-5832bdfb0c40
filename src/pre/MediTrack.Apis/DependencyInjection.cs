using Hangfire;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using MediTrack.Apis.Jobs;
using MediTrack.Apis.Middlewares;
using MediTrack.Apis.MultiTenant;
using MediTrack.Apis.MultiTenant.Services;
using MediTrack.Apis.Services;
using MediTrack.Application.Configs;
using MediTrack.Application.Integrate;
using MediTrack.Application.Schedules;
using MediTrack.Application.Services;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;

namespace MediTrack.Apis
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddPresentationServices(this IServiceCollection services)
        {
            services.AddScoped<ICurrentUserService, CurrentUserService>();
            services.AddScoped<ICurrentHospitalService, CurrentHospitalService>();
            services.AddScoped<IUpdateBookingStatusJob, UpdateBookingStatusJob>();
            return services;
        }

        public static IServiceCollection AddPaymentConfig(this IServiceCollection services,
            IConfiguration configuration)
        {
            var paymentUrl = configuration.GetSection("PAYMENT_PaymentUrl").Value ?? string.Empty;
            var IpnUrl = configuration.GetSection("PAYMENT_IpnUrl").Value ?? string.Empty;
            var paraclinicalIpnUrl = configuration.GetSection("PAYMENT_ParaclinicalIpnUrl").Value ?? string.Empty;
            var membershipIpnUrl = configuration.GetSection("PAYMENT_MembershipIpnUrl").Value ?? string.Empty;
            var donationIpnUrl = configuration.GetSection("PAYMENT_DonationIpnUrl").Value ?? string.Empty;
            var secretKey = configuration.GetSection("PAYMENT_SecretKey").Value ?? string.Empty;

            var partnerIpnUrl = configuration.GetSection("PAYMENT_PartnerIpnUrl").Value ?? string.Empty;
            var descriptionLength = configuration.GetSection("PAYMENT_PartnerDescriptionLength").Value;
            var defaultConfig = new PaymentConfig()
            {
                PaymentUrl = paymentUrl,
                IpnUrl = IpnUrl,
                SecretKey = secretKey,
            };

            var paraclinicalConfig = new PaymentParaclinicalConfig()
            {
                PaymentUrl = paymentUrl,
                IpnUrl = paraclinicalIpnUrl,
                SecretKey = secretKey,
            };
            var membershipConfig = new PaymentMembershipConfig()
            {
                PaymentUrl = paymentUrl,
                IpnUrl = membershipIpnUrl,
                SecretKey = secretKey,
            };

            var paymentPartnerConfig = new PaymentPartnerConfig()
            {
                PaymentUrl = paymentUrl,
                IpnUrl = partnerIpnUrl,
                SecretKey = secretKey,
                LenghtDescription = int.TryParse(descriptionLength, out var length) ? length : 25,
            };

            var donationConfig = new PaymentDonationConfig()
            {
                PaymentUrl = paymentUrl,
                IpnUrl = donationIpnUrl,
                SecretKey = secretKey,
            };


            services.AddSingleton(Options.Create(defaultConfig));
            services.AddSingleton(Options.Create(paraclinicalConfig));
            services.AddSingleton(Options.Create(membershipConfig));
            services.AddSingleton(Options.Create(paymentPartnerConfig));
            services.AddSingleton(Options.Create(donationConfig));

            return services;
        }

        public static IServiceCollection AddEnvironmentConfig(this IServiceCollection services,
            IConfiguration configuration)
        {
            var telegramAPI = configuration.GetSection("TELEGRAM_API").Value ?? string.Empty;
            var telegramChatId = configuration.GetSection("TELEGRAM_CHAT_ID").Value ?? string.Empty;

            var defaultConfig = new EnvironmentConfig()
            {
                TelegramAPI = telegramAPI,
                TelegramChatId = telegramChatId,
            };

            services.AddSingleton(Options.Create(defaultConfig));
            return services;
        }
        /// <summary>
        /// Add the services
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static TenantBuilder<T> AddMultiTenancy<T>(this IServiceCollection Services, Action<MultiTenantOptions<T>>? configure = null) where T : ITenantInfo
        {
            //Register the options
            var options = new MultiTenantOptions<T>();
            configure?.Invoke(options);
            Services.Configure<MultiTenantOptions<T>>(o => configure?.Invoke(o));


            //Provide ambient tenant context
            Services.TryAddSingleton<IMultiTenantContextAccessor<T>, AsyncLocalMultiTenantContextAccessor<T>>();

            //Register middleware to populate the ambient tenant context early in the pipeline
            if (!options.DisableAutomaticPipelineRegistration)
                Services.Insert(0, ServiceDescriptor.Transient<IStartupFilter>(provider => new MultiTenantContextAccessorStartupFilter<T>()));

            return new TenantBuilder<T>(Services, options);
        }

        //Register the multitenant request services middleware manually for more control over operational ordering
        public static IApplicationBuilder UseMultiTenancy<T>(this IApplicationBuilder builder)
            where T : ITenantInfo
        {
            //Check if the startup registration is disabled
            var services = builder.ApplicationServices.GetServices<IStartupFilter>()
                .Where(s => s.GetType().IsGenericType);

            if (services.Any(s => s.GetType().GetGenericTypeDefinition() == typeof(MultiTenantContextAccessorStartupFilter<>)))
                throw new InvalidOperationException("UseMultiTenant must only be called if startup registration is disabled, set 'DisableAutomaticPipelineRegistration' to true");

            if (builder.ApplicationServices.GetServices<IMultiTenantServiceScopeFactory>().Any())
            {
                //Register the multitenant request services middleware with the app pipeline
                return builder.UseMiddleware<MultiTenantContextAccessorMiddleware<T>>()
                    .UseMiddleware<MultiTenantRequestServicesMiddleware<T>>();
            }
            else
            {
                return builder.UseMiddleware<MultiTenantContextAccessorMiddleware<T>>();
            }
        }

        public static IServiceCollection AddHangfireServices(this IServiceCollection services, IConfiguration config)
        {
            var migrationOptions = new MongoMigrationOptions
            {
                MigrationStrategy = new MigrateMongoMigrationStrategy()

            };
            services.AddHangfire(c =>
            {
                c.SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseMongoStorage(config["MONGO_DATABASE_URI"], config["MONGO_DATABASE_HANGFIRE"], new MongoStorageOptions
                {
                    MigrationOptions = migrationOptions,
                    CheckQueuedJobsStrategy = CheckQueuedJobsStrategy.TailNotificationsCollection,
                });
            });
            //add dashboard with basic auth
            services.AddHangfireServer();
            return services;

        }

    }
}
