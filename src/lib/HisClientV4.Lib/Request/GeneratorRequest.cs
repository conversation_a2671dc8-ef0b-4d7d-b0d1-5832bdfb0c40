﻿namespace HisClientV4.Lib.Request
{
    public class GeneratorRequest
    {
        public string tenNb { get; set; } = string.Empty;
        public string ngaySinh { get; set; } = string.Empty;
        public bool uuTien { get; set; }
        public int dichVuId { get; set; }
        public int phongId { get; set; }
        public int doiTuong { get; set; }
        public int khuVucId { get; set; }
        public string? maTheBhyt { get; set; }
        public string? tinhThanhPhoId { get; set; }
        public string? quanHuyenId { get; set; }
        public string? xaPhuongId { get; set; }
        public string tuNgayTheBhyt { get; set; } = string.Empty;
        public string denNgayTheBhyt { get; set; } = string.Empty;
        public string maNoiDKBD { get; set; } = string.Empty;
        public string diaChiTheBhyt { get; set; } = string.Empty;
        public int tuyen { get; set; }
        public string? soDienThoai { get; set; }
        public int gioiTinh { get; set; }
        public bool nbDichVu { get; set; }
        public string maNb { get; set; } = string.Empty;
        public string? diaChi { get; set; } = string.Empty;
        public string? soCanCuoc { get; set; } = string.Empty;
        public string? ngayCapCccd { get; set; } = string.Empty;
        public string? noiCapCccd { get; set; } = string.Empty;
        public string? lyDoVaoVien { get; set; } = string.Empty;
        public int ngheNghiepId { get; set; }
        public int? danTocId { get; set; }
        public string? ngayBhytNamNam { get; set; } = string.Empty;
        public string? KhuVucSongBhyt { get; set; } = string.Empty;
        public string? loaiDungTuyen { get; set; } = string.Empty;
        public string? tenICDChanDoanTuyenDuoi { get; set; } = string.Empty;
        public string? noiChuyen { get; set; }  = string.Empty;
        public string? soChuyenTuyen { get; set; } = string.Empty;
        public string? ngayChuyenTuyen { get; set; } = string.Empty;
        public string? hinhThucChuyenTuyen { get; set; } = string.Empty;
        public string? lyDoChuyenTuyen { get; set; } = string.Empty;
        public string hoTenNguoiGiamHo { get; set; } = string.Empty;
        public string moiQuanhe { get; set; } = string.Empty;
        public string diaChiNguoiNha { get; set; } = string.Empty;
        public string? dienThoaiNguoiNha { get; set; } = string.Empty;
        public string? hoVaTenBo { get; set; } = string.Empty;
        public string? maBHXHBo { get; set; } = string.Empty;
        public string? hoVaTenMe { get; set; } = string.Empty;
        public string? maBHXHMe { get; set; } = string.Empty;
    }
}
