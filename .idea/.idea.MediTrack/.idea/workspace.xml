<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="MediTrack.Persistence">src/infras/MediTrack.Persistence/MediTrack.Persistence.csproj</projectFile>
    <projectFile kind="Docker">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
    <projectFile profileName="http">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
    <projectFile profileName="https">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ede5b86b-e0f6-4859-9e53-5d469a365aa1" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/core/MediTrack.Application/Features/CustomerLogic/Commands/CreateCustomer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/MediTrack.Application/Features/CustomerLogic/Commands/CreateCustomer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/MediTrack.Application/Features/CustomerLogic/Dtos/CustomerDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/MediTrack.Application/Features/CustomerLogic/Dtos/CustomerDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/MediTrack.Domain/Constants/CustomerConstant.cs" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/MediTrack.Domain/Constants/CustomerConstant.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2qeMHKJVaQrK9myr8TropGaOvz9" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.MediTrack.Apis: https.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feat/handle-insuranceNo-flow-and-others",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.MediTrack.Apis: https">
    <configuration name="MediTrack.Apis: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/pre/MediTrack.Apis/MediTrack.Apis.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Apis: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/pre/MediTrack.Apis/MediTrack.Apis.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Persistence" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/infras/MediTrack.Persistence/MediTrack.Persistence.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="MediTrack.Persistence" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Apis/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="meditrack.apis" />
          <option name="contextFolderPath" value="$PROJECT_DIR$" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="src/pre/MediTrack.Apis/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="true" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ede5b86b-e0f6-4859-9e53-5d469a365aa1" name="Changes" comment="" />
      <created>1735018498821</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735018498821</updated>
      <workItem from="1735018499865" duration="872000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
</project>